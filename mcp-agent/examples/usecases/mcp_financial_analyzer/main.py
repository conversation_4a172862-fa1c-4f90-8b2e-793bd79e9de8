"""
Stock Analyzer with Orchestrator and EvaluatorOptimizerLLM Workflow
------------------------------------------------------------
An integrated financial analysis tool using the latest orchestrator implementation
that now supports AugmentedLLM components directly.
"""

import asyncio
import os
import sys
import argparse
import json
from datetime import datetime
from typing import Optional, Dict, Any
from mcp_agent.app import <PERSON><PERSON><PERSON>
from mcp_agent.agents.agent import Agent
from mcp_agent.workflows.orchestrator.orchestrator import Orchestrator
from mcp_agent.workflows.llm.augmented_llm import RequestParams
from mcp_agent.workflows.llm.augmented_llm_vllm import VLLMAugmentedLLM
from mcp_agent.workflows.evaluator_optimizer.evaluator_optimizer import (
    EvaluatorOptimizerLLM,
    QualityRating,
)

# Import our new agent factories
from agents.research_agent import create_research_agent
from agents.analyst_agent import create_analyst_agent
from agents.report_writer import create_report_writer

# Import AG-UI components
from ag_ui_sample.state_manager import AGUIStateManager
from ag_ui_sample.event_stream import FinancialAnalysisEventStream
from ag_ui_sample.tool_bridge import MCPToAGUIToolBridge
from ag_ui_sample.agent_wrappers import AGUIResearchAgent, AGUIAnalystAgent, AGUIReportWriter

# Configuration values
OUTPUT_DIR = "company_reports"

def parse_arguments():
    """Parse command line arguments for both MCP and AG-UI modes."""
    parser = argparse.ArgumentParser(
        description="Financial Analyzer with MCP and AG-UI support",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Standard MCP mode
  python main.py "Apple Inc."

  # AG-UI mode with streaming
  python main.py "Apple Inc." --enable-agui --agui-port 8080

  # AG-UI mode with custom configuration
  python main.py "Apple Inc." --enable-agui --agui-port 3000 --agui-host 0.0.0.0
        """
    )

    # Company name (positional argument)
    parser.add_argument(
        "company",
        nargs="?",
        default="Apple",
        help="Company name to analyze (default: Apple)"
    )

    # AG-UI options
    parser.add_argument(
        "--enable-agui",
        action="store_true",
        help="Enable AG-UI protocol with real-time streaming"
    )

    parser.add_argument(
        "--agui-port",
        type=int,
        default=8080,
        help="Port for AG-UI WebSocket server (default: 8080)"
    )

    parser.add_argument(
        "--agui-host",
        type=str,
        default="localhost",
        help="Host for AG-UI WebSocket server (default: localhost)"
    )

    parser.add_argument(
        "--agui-cors-origins",
        type=str,
        nargs="*",
        default=["http://localhost:3000"],
        help="CORS origins for AG-UI server (default: http://localhost:3000)"
    )

    args = parser.parse_args()

    # Validate company name
    if args.company.startswith('--') or args.company.lower() in ['--company', 'company']:
        parser.error("Please provide a valid company name, not a placeholder or flag")

    # Validate AG-UI port
    if args.enable_agui and not (1000 <= args.agui_port <= 65535):
        parser.error("AG-UI port must be between 1000 and 65535")

    return args

# Parse command line arguments
ARGS = parse_arguments()
COMPANY_NAME = ARGS.company
MAX_ITERATIONS = 3

# Initialize app
app = MCPApp(name="unified_stock_analyzer", human_input_callback=None)


class AGUIOrchestrator:
    """
    AG-UI Orchestrator for coordinating financial analysis workflow
    with real-time streaming capabilities.
    """

    def __init__(
        self,
        company_name: str,
        output_path: str,
        context: Any,
        logger: Any
    ):
        """Initialize AG-UI orchestrator with streaming components."""
        self.company_name = company_name
        self.output_path = output_path
        self.context = context
        self.logger = logger

        # Initialize AG-UI components
        self.state_manager = AGUIStateManager()
        self.event_stream = FinancialAnalysisEventStream()
        self.tool_bridge = MCPToAGUIToolBridge()

        # Initialize AG-UI agent wrappers
        self.research_agent = AGUIResearchAgent(
            company_name=company_name,
            state_manager=self.state_manager,
            event_stream=self.event_stream,
            tool_bridge=self.tool_bridge,
            enhanced=True
        )

        self.analyst_agent = AGUIAnalystAgent(
            company_name=company_name,
            state_manager=self.state_manager,
            event_stream=self.event_stream,
            tool_bridge=self.tool_bridge,
            enhanced=True
        )

        self.report_writer = AGUIReportWriter(
            company_name=company_name,
            output_path=output_path,
            state_manager=self.state_manager,
            event_stream=self.event_stream,
            tool_bridge=self.tool_bridge
        )

    async def execute_workflow(self, thread_id: str) -> Dict[str, Any]:
        """
        Execute complete financial analysis workflow with AG-UI streaming.

        Args:
            thread_id: AG-UI thread ID for event tracking

        Returns:
            Complete workflow results
        """
        try:
            self.logger.info(f"Starting AG-UI workflow for {self.company_name}")

            # Phase 1: Research with quality control
            self.logger.info("Phase 1: Research and data collection")
            research_results = await self.research_agent.execute_agent_workflow(
                thread_id=thread_id,
                context=self.context
            )

            if research_results["status"] != "success":
                raise Exception(f"Research phase failed: {research_results}")

            # Phase 2: Financial analysis
            self.logger.info("Phase 2: Financial analysis")
            analysis_results = await self.analyst_agent.execute_agent_workflow(
                thread_id=thread_id,
                research_data=research_results["results"],
                context=self.context
            )

            if analysis_results["status"] != "success":
                raise Exception(f"Analysis phase failed: {analysis_results}")

            # Phase 3: Report generation
            self.logger.info("Phase 3: Report generation")
            report_results = await self.report_writer.execute_agent_workflow(
                thread_id=thread_id,
                research_data=research_results["results"],
                analysis_data=analysis_results,
                context=self.context
            )

            if report_results["status"] != "success":
                raise Exception(f"Report generation failed: {report_results}")

            # Final workflow results
            workflow_results = {
                "status": "success",
                "company": self.company_name,
                "workflow": "complete",
                "phases": {
                    "research": research_results,
                    "analysis": analysis_results,
                    "reporting": report_results
                },
                "output_file": self.output_path,
                "metadata": {
                    "thread_id": thread_id,
                    "total_phases": 3,
                    "enhanced_mode": True
                }
            }

            self.logger.info(f"AG-UI workflow completed successfully for {self.company_name}")
            return workflow_results

        except Exception as e:
            self.logger.error(f"AG-UI workflow failed: {str(e)}")

            # Stream error event
            await self.event_stream.stream_text_message(
                content=f"❌ Workflow failed: {str(e)}",
                thread_id=thread_id,
                role="assistant",
                metadata={
                    "status": "error",
                    "error_type": type(e).__name__
                }
            )

            return {
                "status": "error",
                "error": str(e),
                "company": self.company_name,
                "thread_id": thread_id
            }


async def run_mcp_mode(context, logger, output_path: str) -> bool:
    """Run traditional MCP mode workflow."""
    logger.info(f"Running in MCP mode for {COMPANY_NAME}")

    # --- DEFINE AGENTS ---

    # Research agent: Collects data using Google Search
    # Using enhanced BaseAgentWrapper with atomic-agents capabilities
    research_agent = create_research_agent(COMPANY_NAME)

    # Research evaluator: Evaluates the quality of research
    research_evaluator = Agent(
        name="research_evaluator",
        instruction=f"""You are an expert research evaluator specializing in financial data quality.

        Evaluate the research data on {COMPANY_NAME} based on these criteria:

        1. Accuracy: Are facts properly cited with source URLs? Are numbers precise?
        2. Completeness: Is all required information present? (stock price, earnings data, recent news)
        3. Specificity: Are exact figures provided rather than generalizations?
        4. Clarity: Is the information organized and easy to understand?

        For each criterion, provide a rating:
        - EXCELLENT: Exceeds requirements, highly reliable
        - GOOD: Meets all requirements, reliable
        - FAIR: Missing some elements but usable
        - POOR: Missing critical information, not usable

        Provide an overall quality rating and specific feedback on what needs improvement.
        If any critical financial data is missing (stock price, earnings figures), the overall
        rating should not exceed FAIR.""",
    )

    # Create the research EvaluatorOptimizerLLM component
    research_quality_controller = EvaluatorOptimizerLLM(
        optimizer=research_agent,
        evaluator=research_evaluator,
        llm_factory=VLLMAugmentedLLM,
        min_rating=QualityRating.EXCELLENT,
    )

    # Analyst agent: Analyzes the research data
    # Using enhanced BaseAgentWrapper with atomic-agents capabilities
    analyst_agent = create_analyst_agent(COMPANY_NAME)

    # Report writer: Creates the final report
    # Using enhanced BaseAgentWrapper with atomic-agents capabilities
    report_writer = create_report_writer(COMPANY_NAME, output_path)

    # --- CREATE THE ORCHESTRATOR ---
    logger.info(f"Initializing stock analysis workflow for {COMPANY_NAME}")

    # The updated Orchestrator can now take AugmentedLLM instances directly
    orchestrator = Orchestrator(
        llm_factory=VLLMAugmentedLLM,
        available_agents=[
            # We can now pass the EvaluatorOptimizerLLM directly as a component
            research_quality_controller,
            analyst_agent,
            report_writer,
        ],
        plan_type="full",
    )

    # Define the task for the orchestrator
    task = f"""Create a high-quality stock analysis report for {COMPANY_NAME} by following these steps:

    1. Use the EvaluatorOptimizerLLM component (named 'research_quality_controller') to gather high-quality
       financial data about {COMPANY_NAME}. This component will automatically evaluate
       and improve the research until it reaches EXCELLENT quality.

       Ask for:
       - Current stock price and recent movement
       - Latest quarterly earnings results and performance vs expectations
       - Recent news and developments

    2. Use the financial_analyst to analyze this research data and identify key insights.

    3. Use the report_writer to create a comprehensive stock report and save it to:
       "{output_path}"

    The final report should be professional, fact-based, and include all relevant financial information."""

    # Validate company name before starting workflow
    if not COMPANY_NAME or COMPANY_NAME.startswith('--'):
        logger.error(f"Invalid company name: {COMPANY_NAME}")
        return False

    logger.info(f"Starting analysis for company: {COMPANY_NAME}")
    logger.info("Starting the stock analysis workflow")
    try:
        await orchestrator.generate_str(
            message=task, request_params=RequestParams(model="Qwen/Qwen3-32B")
        )

        # Check if report was successfully created
        if os.path.exists(output_path):
            logger.info(f"Report successfully generated: {output_path}")
            return True
        else:
            logger.error(f"Failed to create report at {output_path}")
            return False

    except Exception as e:
        logger.error(f"Error during workflow execution: {str(e)}")
        return False


async def run_agui_mode(context, logger, output_path: str) -> bool:
    """Run AG-UI mode workflow with real-time streaming."""
    logger.info(f"Running in AG-UI mode for {COMPANY_NAME}")

    # Generate thread ID for AG-UI tracking
    import uuid
    thread_id = str(uuid.uuid4())

    # Create AG-UI orchestrator
    agui_orchestrator = AGUIOrchestrator(
        company_name=COMPANY_NAME,
        output_path=output_path,
        context=context,
        logger=logger
    )

    try:
        # Execute AG-UI workflow
        results = await agui_orchestrator.execute_workflow(thread_id)

        if results["status"] == "success":
            logger.info(f"AG-UI workflow completed successfully")
            logger.info(f"Report generated: {output_path}")
            return True
        else:
            logger.error(f"AG-UI workflow failed: {results.get('error', 'Unknown error')}")
            return False

    except Exception as e:
        logger.error(f"Error during AG-UI workflow execution: {str(e)}")
        return False


async def main():
    # Create output directory and set up file paths
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"{COMPANY_NAME.lower().replace(' ', '_')}_report_{timestamp}.md"
    output_path = os.path.join(OUTPUT_DIR, output_file)

    async with app.run() as analyzer_app:
        context = analyzer_app.context
        logger = analyzer_app.logger

        # Configure filesystem server to use current directory
        if "filesystem" in context.config.mcp.servers:
            context.config.mcp.servers["filesystem"].args.extend([os.getcwd()])
            logger.info("Filesystem server configured")
        else:
            logger.warning("Filesystem server not configured - report saving may fail")

        # Check for g-search server
        if "g-search" not in context.config.mcp.servers:
            logger.warning(
                "Google Search server not found! This script requires g-search-mcp"
            )
            logger.info("You can install it with: npm install -g g-search-mcp")
            return False

        # Choose execution mode based on command-line arguments
        if ARGS.enable_agui:
            logger.info("🚀 AG-UI mode enabled - Starting with real-time streaming")
            logger.info(f"AG-UI Configuration:")
            logger.info(f"  - Host: {ARGS.agui_host}")
            logger.info(f"  - Port: {ARGS.agui_port}")
            logger.info(f"  - CORS Origins: {ARGS.agui_cors_origins}")

            success = await run_agui_mode(context, logger, output_path)
        else:
            logger.info("📊 MCP mode - Running traditional workflow")
            success = await run_mcp_mode(context, logger, output_path)

        return success


if __name__ == "__main__":
    asyncio.run(main())
