"""
HTTP Server for MCP Financial Analyzer with AG-UI Protocol Support
================================================================

FastAPI-based HTTP server that exposes the MCP Financial Analyzer's AG-UI workflow
as an HTTP endpoint with Server-Sent Events (SSE) streaming, following AG-UI protocol standards.

This server provides:
- HTTP POST endpoint accepting RunAgentInput schema
- SSE streaming of BaseEvent objects
- Integration with existing AG-UI workflow components
- Comprehensive error handling and logging
- CORS support for frontend integration

Usage:
    python http_server.py --port 8080 --host localhost

Example request:
    POST /analyze
    Content-Type: application/json
    Accept: text/event-stream
    
    {
        "threadId": "thread_123",
        "runId": "run_456", 
        "state": {},
        "messages": [],
        "tools": [],
        "context": [],
        "forwardedProps": {}
    }
"""

import asyncio
import argparse
import logging
import uuid
from datetime import datetime
from typing import Dict, Any, Optional
import uvicorn
from fastapi import FastAPI, Request, HTTPException
from fastapi.responses import StreamingResponse
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager

# Import AG-UI core components from LlamaIndex protocols
from ag_ui.core.types import RunAgentInput
from ag_ui_sample.events import (
    BaseEvent, EventType, RunAgentInput, RunStartedEvent, RunFinishedEvent, RunErrorEvent,
    TextMessageStartEvent, TextMessageContentEvent, TextMessageEndEvent, EventEncoder
)

# Import MCP Agent components
from mcp_agent.app import MCPApp

# Import AG-UI orchestrator
from main import AGUIOrchestrator

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Global variables for MCP app context
mcp_app_context = None
mcp_logger = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """FastAPI lifespan context manager for MCP app initialization."""
    global mcp_app_context, mcp_logger
    
    logger.info("Initializing MCP Financial Analyzer HTTP Server...")
    
    # Initialize MCP app
    mcp_app = MCPApp(name="financial_analyzer_http_server", human_input_callback=None)
    
    try:
        # Start MCP app context
        async with mcp_app.run() as analyzer_app:
            mcp_app_context = analyzer_app.context
            mcp_logger = analyzer_app.logger
            
            # Configure filesystem server
            if "filesystem" in mcp_app_context.config.mcp.servers:
                import os
                mcp_app_context.config.mcp.servers["filesystem"].args.extend([os.getcwd()])
                mcp_logger.info("Filesystem server configured")
            
            # Check for required servers
            if "g-search" not in mcp_app_context.config.mcp.servers:
                mcp_logger.warning("Google Search server not found! Install with: npm install -g g-search-mcp")
            
            logger.info("MCP Financial Analyzer HTTP Server initialized successfully")
            yield
            
    except Exception as e:
        logger.error(f"Failed to initialize MCP app: {e}")
        raise
    finally:
        logger.info("Shutting down MCP Financial Analyzer HTTP Server...")


# Create FastAPI app with lifespan
app = FastAPI(
    title="MCP Financial Analyzer HTTP Server",
    description="HTTP API for MCP Financial Analyzer with AG-UI Protocol Support",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "service": "MCP Financial Analyzer HTTP Server"
    }


@app.post("/analyze")
async def analyze_company(input_data: RunAgentInput, request: Request):
    """
    Main analysis endpoint that accepts RunAgentInput and returns SSE stream of BaseEvent objects.
    
    Args:
        input_data: RunAgentInput containing analysis parameters
        request: FastAPI request object for header inspection
        
    Returns:
        StreamingResponse with Server-Sent Events containing BaseEvent objects
    """
    global mcp_app_context, mcp_logger
    
    if not mcp_app_context or not mcp_logger:
        raise HTTPException(status_code=503, detail="MCP app not initialized")
    
    # Get accept header for proper encoding
    accept_header = request.headers.get("accept", "text/event-stream")
    
    # Create event encoder
    encoder = EventEncoder(accept=accept_header)
    
    # Extract company name from context or use default
    company_name = "Apple Inc."  # Default fallback
    for context_item in input_data.context:
        if context_item.description.lower() in ["company", "company_name", "target_company"]:
            company_name = context_item.value
            break
    
    # If no company in context, try to extract from messages
    if company_name == "Apple Inc." and input_data.messages:
        last_message = input_data.messages[-1]
        if hasattr(last_message, 'content') and last_message.content:
            # Simple extraction - in production, use more sophisticated parsing
            content = last_message.content.lower()
            if "analyze" in content or "company" in content:
                # Extract company name from message content
                words = last_message.content.split()
                for i, word in enumerate(words):
                    if word.lower() in ["analyze", "company"] and i + 1 < len(words):
                        company_name = words[i + 1].strip('.,!?')
                        break
    
    logger.info(f"Starting analysis for company: {company_name}")
    logger.info(f"Thread ID: {input_data.thread_id}, Run ID: {input_data.run_id}")
    
    async def event_generator():
        """Generate SSE events for the financial analysis workflow."""
        try:
            # Send run started event
            run_started = RunStartedEvent(
                type=EventType.RUN_STARTED,
                timestamp=int(datetime.now().timestamp() * 1000)
            )
            yield encoder.encode(run_started)
            
            # Create output path for report
            import os
            from datetime import datetime
            output_dir = "company_reports"
            os.makedirs(output_dir, exist_ok=True)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"{company_name.lower().replace(' ', '_')}_report_{timestamp}.md"
            output_path = os.path.join(output_dir, output_file)
            
            # Create AG-UI orchestrator
            agui_orchestrator = AGUIOrchestrator(
                company_name=company_name,
                output_path=output_path,
                context=mcp_app_context,
                logger=mcp_logger
            )
            
            # Send initial status message
            msg_id = str(uuid.uuid4())
            yield encoder.encode(TextMessageStartEvent(
                type=EventType.TEXT_MESSAGE_START,
                message_id=msg_id,
                role="assistant",
                timestamp=int(datetime.now().timestamp() * 1000)
            ))
            
            yield encoder.encode(TextMessageContentEvent(
                type=EventType.TEXT_MESSAGE_CONTENT,
                message_id=msg_id,
                delta=f"🚀 Starting comprehensive financial analysis for {company_name}...\n\n",
                timestamp=int(datetime.now().timestamp() * 1000)
            ))
            
            # Execute AG-UI workflow
            results = await agui_orchestrator.execute_workflow(input_data.thread_id)
            
            if results["status"] == "success":
                yield encoder.encode(TextMessageContentEvent(
                    type=EventType.TEXT_MESSAGE_CONTENT,
                    message_id=msg_id,
                    delta=f"✅ Analysis completed successfully!\n\n📊 Report generated: {output_path}\n\n",
                    timestamp=int(datetime.now().timestamp() * 1000)
                ))
                
                # Include summary of results if available
                if "phases" in results:
                    yield encoder.encode(TextMessageContentEvent(
                        type=EventType.TEXT_MESSAGE_CONTENT,
                        message_id=msg_id,
                        delta="📈 **Analysis Summary:**\n",
                        timestamp=int(datetime.now().timestamp() * 1000)
                    ))
                    
                    for phase_name, phase_results in results["phases"].items():
                        status_emoji = "✅" if phase_results.get("status") == "success" else "❌"
                        yield encoder.encode(TextMessageContentEvent(
                            type=EventType.TEXT_MESSAGE_CONTENT,
                            message_id=msg_id,
                            delta=f"- {status_emoji} {phase_name.title()} Phase\n",
                            timestamp=int(datetime.now().timestamp() * 1000)
                        ))
                
            else:
                error_msg = results.get('error', 'Unknown error occurred')
                yield encoder.encode(TextMessageContentEvent(
                    type=EventType.TEXT_MESSAGE_CONTENT,
                    message_id=msg_id,
                    delta=f"❌ Analysis failed: {error_msg}\n\n",
                    timestamp=int(datetime.now().timestamp() * 1000)
                ))
            
            # End text message
            yield encoder.encode(TextMessageEndEvent(
                type=EventType.TEXT_MESSAGE_END,
                message_id=msg_id,
                timestamp=int(datetime.now().timestamp() * 1000)
            ))
            
            # Send run finished event
            run_finished = RunFinishedEvent(
                type=EventType.RUN_FINISHED,
                timestamp=int(datetime.now().timestamp() * 1000)
            )
            yield encoder.encode(run_finished)
            
        except Exception as e:
            logger.error(f"Error during analysis workflow: {e}")
            
            # Send error event
            error_event = RunErrorEvent(
                type=EventType.RUN_ERROR,
                timestamp=int(datetime.now().timestamp() * 1000)
            )
            yield encoder.encode(error_event)
            
            # Send error message
            error_msg_id = str(uuid.uuid4())
            yield encoder.encode(TextMessageStartEvent(
                type=EventType.TEXT_MESSAGE_START,
                message_id=error_msg_id,
                role="assistant",
                timestamp=int(datetime.now().timestamp() * 1000)
            ))
            
            yield encoder.encode(TextMessageContentEvent(
                type=EventType.TEXT_MESSAGE_CONTENT,
                message_id=error_msg_id,
                delta=f"❌ **Error during analysis:** {str(e)}\n\nPlease try again or contact support if the issue persists.\n",
                timestamp=int(datetime.now().timestamp() * 1000)
            ))
            
            yield encoder.encode(TextMessageEndEvent(
                type=EventType.TEXT_MESSAGE_END,
                message_id=error_msg_id,
                timestamp=int(datetime.now().timestamp() * 1000)
            ))
    
    # Return streaming response
    return StreamingResponse(
        event_generator(),
        media_type=encoder.get_content_type(),
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "X-Accel-Buffering": "no"  # Disable nginx buffering
        }
    )


def parse_arguments():
    """Parse command line arguments for the HTTP server."""
    parser = argparse.ArgumentParser(
        description="MCP Financial Analyzer HTTP Server with AG-UI Protocol Support"
    )

    parser.add_argument(
        "--host",
        type=str,
        default="localhost",
        help="Host to bind the server to (default: localhost)"
    )

    parser.add_argument(
        "--port",
        type=int,
        default=8080,
        help="Port to bind the server to (default: 8080)"
    )

    parser.add_argument(
        "--reload",
        action="store_true",
        help="Enable auto-reload for development"
    )

    parser.add_argument(
        "--log-level",
        type=str,
        default="info",
        choices=["debug", "info", "warning", "error"],
        help="Log level (default: info)"
    )

    return parser.parse_args()


def main():
    """Main entry point for the HTTP server."""
    args = parse_arguments()

    logger.info(f"Starting MCP Financial Analyzer HTTP Server on {args.host}:{args.port}")

    uvicorn.run(
        "http_server:app",
        host=args.host,
        port=args.port,
        reload=args.reload,
        log_level=args.log_level
    )


if __name__ == "__main__":
    main()
