"""
Abstract AG-UI Agent
===================

Base class for AG-UI agents that provides event streaming capabilities
while maintaining compatibility with existing MCP agent infrastructure.
"""

import asyncio
from typing import Dict, Any, Optional, List
from datetime import datetime
from abc import ABC, abstractmethod

from .events import (
    RunStartedEvent,
    RunFinishedEvent, 
    RunErrorEvent,
    generate_message_id
)
from .state_manager import AGUIStateManager
from .event_stream import FinancialAnalysisEventStream, EventObserver
from .tool_bridge import MCPToAGUIToolBridge


class RunAgentInput:
    """Input data for AG-UI agent execution"""
    
    def __init__(self, threadId: str, runId: str, messages: List[Dict[str, Any]], **kwargs):
        self.threadId = threadId
        self.runId = runId
        self.messages = messages
        self.metadata = kwargs


class AbstractAgent(ABC):
    """
    Abstract base class for AG-UI agents.
    
    Provides event streaming infrastructure and state management
    while allowing concrete agents to implement specific functionality.
    """
    
    def __init__(self, mcp_agent, agent_name: str):
        """
        Initialize abstract AG-UI agent.
        
        Args:
            mcp_agent: Underlying MCP agent instance
            agent_name: Name of the agent
        """
        self.mcp_agent = mcp_agent
        self.agent_name = agent_name
        self.state_manager = AGUIStateManager()
        self.event_stream = FinancialAnalysisEventStream()
        self.tool_bridge = MCPToAGUIToolBridge()
        
        # Connect state manager to event stream
        self.state_manager.add_observer(self._on_state_change)
    
    def _on_state_change(self, event):
        """Handle state change events"""
        self.event_stream.emit_event(event)
    
    async def run(self, input_data: RunAgentInput) -> Dict[str, Any]:
        """
        Main entry point for AG-UI agent execution.
        
        Args:
            input_data: Input data for agent execution
            
        Returns:
            Observable for event streaming
        """
        def create_observable():
            observer = EventObserver()
            self.event_stream.add_observer(observer)
            
            # Start execution in background
            asyncio.create_task(self._execute_workflow(input_data, observer))
            
            return observer
        
        return create_observable
    
    async def _execute_workflow(self, input_data: RunAgentInput, observer: EventObserver):
        """
        Execute the agent workflow with event streaming.
        
        Args:
            input_data: Input data for execution
            observer: Event observer for streaming
        """
        try:
            # Emit RUN_STARTED event
            start_event = RunStartedEvent.create(
                thread_id=input_data.threadId,
                run_id=input_data.runId,
                input_data={
                    "messages": input_data.messages,
                    "agent_name": self.agent_name,
                    **input_data.metadata
                }
            )
            observer.next(start_event.dict())
            
            # Initialize state
            self.state_manager.update_state({
                "company_name": input_data.metadata.get("company_name", ""),
                "analysis_phase": f"{self.agent_name}_started",
                "current_operation": f"Initializing {self.agent_name}",
                "progress_percentage": 0.0
            }, input_data.threadId)
            
            # Execute agent-specific workflow
            result = await self.execute_agent_workflow(input_data, observer)
            
            # Emit RUN_FINISHED event
            finish_event = RunFinishedEvent.create(
                thread_id=input_data.threadId,
                run_id=input_data.runId,
                output_data={
                    "result": result,
                    "agent_name": self.agent_name,
                    "completion_time": datetime.now().isoformat()
                }
            )
            observer.next(finish_event.dict())
            
            # Update final state
            self.state_manager.update_state({
                "analysis_phase": f"{self.agent_name}_completed",
                "progress_percentage": 100.0,
                "current_operation": f"{self.agent_name} completed successfully"
            }, input_data.threadId)
            
            observer.complete()
            
        except Exception as e:
            # Emit RUN_ERROR event
            error_event = RunErrorEvent.create(
                thread_id=input_data.threadId,
                run_id=input_data.runId,
                error_info={
                    "type": type(e).__name__,
                    "message": str(e),
                    "agent_name": self.agent_name,
                    "timestamp": datetime.now().isoformat()
                }
            )
            observer.next(error_event.dict())
            
            # Update error state
            self.state_manager.set_error_state({
                "error": str(e),
                "agent": self.agent_name,
                "timestamp": datetime.now().isoformat()
            }, input_data.threadId)
            
            observer.error(e)
    
    @abstractmethod
    async def execute_agent_workflow(self, input_data: RunAgentInput, observer: EventObserver) -> Any:
        """
        Execute the specific agent workflow.
        
        This method must be implemented by concrete agent classes.
        
        Args:
            input_data: Input data for execution
            observer: Event observer for streaming
            
        Returns:
            Agent execution result
        """
        pass
    
    async def _emit_text_message(self, content: str, observer: EventObserver, 
                                thread_id: str, message_id: Optional[str] = None) -> str:
        """
        Emit streaming text message.
        
        Args:
            content: Message content
            observer: Event observer
            thread_id: Thread ID
            message_id: Optional message ID
            
        Returns:
            Message ID used
        """
        if not message_id:
            message_id = generate_message_id()
        
        return await self.event_stream.stream_text_message(
            content=content,
            thread_id=thread_id,
            message_id=message_id
        )
    
    async def _emit_phase_update(self, phase: str, description: str, 
                               observer: EventObserver, thread_id: str, progress: Optional[float] = None):
        """
        Emit analysis phase update.
        
        Args:
            phase: Phase name
            description: Phase description
            observer: Event observer
            thread_id: Thread ID
            progress: Optional progress percentage
        """
        await self.event_stream.stream_analysis_phase(
            phase_name=phase,
            phase_description=description,
            thread_id=thread_id,
            progress=progress
        )
        
        # Update state
        updates = {
            "analysis_phase": phase,
            "current_operation": description
        }
        if progress is not None:
            updates["progress_percentage"] = progress
        
        self.state_manager.update_state(updates, thread_id)
    
    async def _execute_tool_with_streaming(self, tool_name: str, tool_args: Dict[str, Any],
                                         observer: EventObserver, thread_id: str, 
                                         context: Optional[Any] = None) -> Any:
        """
        Execute tool with AG-UI event streaming.
        
        Args:
            tool_name: Tool name
            tool_args: Tool arguments
            observer: Event observer
            thread_id: Thread ID
            context: Optional MCP context
            
        Returns:
            Tool execution result
        """
        return await self.tool_bridge.execute_mcp_tool_with_agui_events(
            tool_name=tool_name,
            tool_args=tool_args,
            observer=observer.next,
            thread_id=thread_id,
            context=context
        )
    
    def get_agent_info(self) -> Dict[str, Any]:
        """Get agent information"""
        return {
            "name": self.agent_name,
            "type": "AG-UI Agent",
            "mcp_agent": str(type(self.mcp_agent).__name__) if self.mcp_agent else None,
            "state": self.state_manager.get_progress_info() if hasattr(self, 'state_manager') else {},
            "stream_stats": self.event_stream.get_stream_stats() if hasattr(self, 'event_stream') else {}
        }

    @abstractmethod
    async def execute_agent_workflow(
        self,
        thread_id: str,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Execute the agent's workflow with AG-UI streaming.

        Args:
            thread_id: AG-UI thread ID
            **kwargs: Additional workflow parameters

        Returns:
            Workflow results with metadata
        """
        pass

    async def _update_phase_state(
        self,
        thread_id: str,
        phase: str,
        progress: float
    ) -> None:
        """
        Update the current phase state and progress.

        Args:
            thread_id: AG-UI thread ID
            phase: Current phase name
            progress: Progress percentage (0.0 to 100.0)
        """
        if hasattr(self, 'state_manager'):
            self.state_manager.set_analysis_phase(phase, thread_id, progress)

    async def _handle_workflow_error(
        self,
        error: Exception,
        thread_id: str,
        phase: str
    ) -> None:
        """
        Handle workflow errors with proper streaming.

        Args:
            error: The exception that occurred
            thread_id: AG-UI thread ID
            phase: Current phase when error occurred
        """
        if hasattr(self, 'event_stream'):
            await self.event_stream.stream_text_message(
                content=f"❌ Error in {phase} phase: {str(error)}",
                thread_id=thread_id,
                role="assistant",
                metadata={
                    "phase": phase,
                    "status": "error",
                    "error_type": type(error).__name__
                }
            )
