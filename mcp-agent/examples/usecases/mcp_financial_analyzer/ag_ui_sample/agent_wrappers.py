"""
AG-UI Agent Wrappers for Financial Analysis
==========================================

AG-UI enabled versions of the financial analysis agents that provide
real-time streaming capabilities while maintaining full compatibility
with existing MCP agent functionality.
"""

import asyncio
from typing import Dict, Any, Optional, List
from agents.base_agent_wrapper import BaseAgentWrapper
from agents.research_agent import create_enhanced_research_agent
from agents.analyst_agent import create_enhanced_analyst_agent
from agents.report_writer import create_report_writer
from ag_ui_sample.abstract_agent import AbstractAgent
from ag_ui_sample.state_manager import AGUIStateManager
from ag_ui_sample.event_stream import FinancialAnalysisEventStream
from ag_ui_sample.tool_bridge import MCPTo<PERSON>UIToolBridge


class AGUIResearchAgent(AbstractAgent):
    """
    AG-UI enabled research agent with real-time streaming capabilities.
    
    Extends the existing research agent functionality with AG-UI protocol support
    for streaming research progress, tool calls, and results.
    """
    
    def __init__(
        self,
        company_name: str,
        state_manager: AGUIStateManager,
        event_stream: FinancialAnalysisEventStream,
        tool_bridge: MCPToAGUIToolBridge,
        enhanced: bool = True
    ):
        """
        Initialize AG-UI research agent.
        
        Args:
            company_name: Company to research
            state_manager: AG-UI state manager
            event_stream: Event streaming infrastructure
            tool_bridge: MCP to AG-UI tool bridge
            enhanced: Whether to use enhanced research agent
        """
        self.company_name = company_name
        self.enhanced = enhanced

        # Create underlying MCP agent
        if enhanced:
            self.mcp_agent = create_enhanced_research_agent(company_name)
        else:
            from agents.research_agent import create_research_agent
            self.mcp_agent = create_research_agent(company_name)

        # Initialize parent with MCP agent and name
        super().__init__(self.mcp_agent, "Research Agent")

        # Store AG-UI components
        self.state_manager = state_manager
        self.event_stream = event_stream
        self.tool_bridge = tool_bridge

        # Agent configuration
        self.agent_name = "Research Agent"
        self.phase_name = "Research Phase"
        
    async def execute_agent_workflow(
        self,
        thread_id: str,
        context: Optional[Any] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Execute research workflow with AG-UI streaming.
        
        Args:
            thread_id: AG-UI thread ID
            context: MCP context for tool execution
            **kwargs: Additional workflow parameters
            
        Returns:
            Research results with metadata
        """
        try:
            # Update state to research phase
            await self._update_phase_state(thread_id, "research", 0.0)
            
            # Stream phase start
            await self.event_stream.stream_analysis_phase(
                phase_name=self.phase_name,
                phase_description=f"Gathering comprehensive financial data for {self.company_name}",
                thread_id=thread_id,
                progress=0.0
            )
            
            # Stream agent activity
            await self.event_stream.stream_agent_activity(
                agent_name=self.agent_name,
                activity="Initializing financial research",
                thread_id=thread_id,
                details=f"Preparing to research {self.company_name} using multiple data sources"
            )
            
            # Execute research queries with streaming
            research_results = await self._execute_research_queries(thread_id, context)
            
            # Update progress
            await self._update_phase_state(thread_id, "research", 75.0)
            
            # Process and validate results
            processed_results = await self._process_research_results(
                research_results, thread_id
            )
            
            # Final state update
            await self._update_phase_state(thread_id, "research", 100.0)
            
            # Stream completion
            await self.event_stream.stream_text_message(
                content=f"✅ Research phase completed for {self.company_name}",
                thread_id=thread_id,
                role="assistant",
                metadata={
                    "phase": "research",
                    "status": "completed",
                    "agent_name": self.agent_name
                }
            )
            
            return {
                "status": "success",
                "agent": self.agent_name,
                "phase": "research",
                "company": self.company_name,
                "results": processed_results,
                "metadata": {
                    "queries_executed": len(research_results),
                    "enhanced_mode": self.enhanced,
                    "data_quality": "high"
                }
            }
            
        except Exception as e:
            # Handle errors with streaming
            await self._handle_workflow_error(e, thread_id, "research")
            raise
    
    async def _execute_research_queries(
        self,
        thread_id: str,
        context: Optional[Any] = None
    ) -> List[Dict[str, Any]]:
        """Execute research queries with tool call streaming."""
        
        # Define research queries based on agent type
        if self.enhanced:
            queries = [
                f"{self.company_name} stock price today current",
                f"{self.company_name} latest quarterly earnings results",
                f"{self.company_name} financial news recent developments",
                f"{self.company_name} earnings expectations analyst forecasts",
                f"{self.company_name} SEC filings recent"
            ]
        else:
            queries = [
                f"{self.company_name} stock price today",
                f"{self.company_name} latest quarterly earnings",
                f"{self.company_name} financial news",
                f"{self.company_name} earnings expectations"
            ]
        
        results = []
        total_queries = len(queries)
        
        for i, query in enumerate(queries):
            # Stream tool execution
            await self.event_stream.stream_tool_execution(
                tool_name="search_financial_data",
                operation=f"Searching for: {query}",
                thread_id=thread_id,
                details={"query": query, "source": "Google Search"}
            )
            
            # Execute search with tool bridge
            try:
                search_result = await self.tool_bridge.execute_mcp_tool_with_agui_events(
                    tool_name="g-search",
                    tool_args={"query": query},
                    observer=self.event_stream.emit_event,
                    thread_id=thread_id,
                    context=context
                )
                
                results.append({
                    "query": query,
                    "result": search_result,
                    "status": "success"
                })
                
                # Update progress
                progress = ((i + 1) / total_queries) * 50.0  # Research is 50% of total
                await self._update_phase_state(thread_id, "research", progress)
                
            except Exception as e:
                results.append({
                    "query": query,
                    "result": None,
                    "status": "error",
                    "error": str(e)
                })
                
                # Stream error but continue
                await self.event_stream.stream_text_message(
                    content=f"⚠️ Search query failed: {query} - {str(e)}",
                    thread_id=thread_id,
                    role="assistant"
                )
        
        return results
    
    async def _process_research_results(
        self,
        raw_results: List[Dict[str, Any]],
        thread_id: str
    ) -> Dict[str, Any]:
        """Process and structure research results."""
        
        await self.event_stream.stream_agent_activity(
            agent_name=self.agent_name,
            activity="Processing research data",
            thread_id=thread_id,
            details="Analyzing and structuring collected financial information"
        )
        
        # Categorize results
        processed = {
            "stock_data": [],
            "earnings_data": [],
            "news_data": [],
            "analyst_data": [],
            "sec_filings": [],
            "summary": {
                "total_queries": len(raw_results),
                "successful_queries": len([r for r in raw_results if r["status"] == "success"]),
                "failed_queries": len([r for r in raw_results if r["status"] == "error"])
            }
        }
        
        # Process each result
        for result in raw_results:
            query = result["query"].lower()
            
            if "stock price" in query:
                processed["stock_data"].append(result)
            elif "earnings" in query:
                processed["earnings_data"].append(result)
            elif "news" in query:
                processed["news_data"].append(result)
            elif "analyst" in query or "forecast" in query:
                processed["analyst_data"].append(result)
            elif "sec" in query or "filing" in query:
                processed["sec_filings"].append(result)
        
        # Stream processing completion
        await self.event_stream.stream_text_message(
            content=f"📊 Processed {processed['summary']['successful_queries']} successful research queries",
            thread_id=thread_id,
            role="assistant",
            metadata={
                "processing_stats": processed["summary"],
                "agent_name": self.agent_name
            }
        )
        
        return processed
    
    def get_agent_info(self) -> Dict[str, Any]:
        """Get agent information for AG-UI display."""
        return {
            "name": self.agent_name,
            "type": "research",
            "company": self.company_name,
            "enhanced": self.enhanced,
            "capabilities": [
                "Financial data search",
                "Stock price research",
                "Earnings data collection",
                "News analysis",
                "SEC filing research" if self.enhanced else None
            ],
            "tools": ["g-search", "fetch"],
            "phase": self.phase_name
        }


class AGUIAnalystAgent(AbstractAgent):
    """
    AG-UI enabled analyst agent with real-time streaming capabilities.
    
    Extends the existing analyst agent functionality with AG-UI protocol support
    for streaming analysis progress and results.
    """
    
    def __init__(
        self,
        company_name: str,
        state_manager: AGUIStateManager,
        event_stream: FinancialAnalysisEventStream,
        tool_bridge: MCPToAGUIToolBridge,
        enhanced: bool = True
    ):
        """
        Initialize AG-UI analyst agent.
        
        Args:
            company_name: Company to analyze
            state_manager: AG-UI state manager
            event_stream: Event streaming infrastructure
            tool_bridge: MCP to AG-UI tool bridge
            enhanced: Whether to use enhanced analyst agent
        """
        self.company_name = company_name
        self.enhanced = enhanced

        # Create underlying MCP agent
        if enhanced:
            self.mcp_agent = create_enhanced_analyst_agent(company_name)
        else:
            from agents.analyst_agent import create_analyst_agent
            self.mcp_agent = create_analyst_agent(company_name)

        # Initialize parent with MCP agent and name
        super().__init__(self.mcp_agent, "Analyst Agent")

        # Store AG-UI components
        self.state_manager = state_manager
        self.event_stream = event_stream
        self.tool_bridge = tool_bridge

        # Agent configuration
        self.agent_name = "Analyst Agent"
        self.phase_name = "Analysis Phase"

    async def execute_agent_workflow(
        self,
        thread_id: str,
        research_data: Dict[str, Any],
        context: Optional[Any] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Execute analysis workflow with AG-UI streaming.

        Args:
            thread_id: AG-UI thread ID
            research_data: Research results from research agent
            context: MCP context for tool execution
            **kwargs: Additional workflow parameters

        Returns:
            Analysis results with metadata
        """
        try:
            # Update state to analysis phase
            await self._update_phase_state(thread_id, "analysis", 0.0)

            # Stream phase start
            await self.event_stream.stream_analysis_phase(
                phase_name=self.phase_name,
                phase_description=f"Analyzing financial data for {self.company_name}",
                thread_id=thread_id,
                progress=0.0
            )

            # Stream agent activity
            await self.event_stream.stream_agent_activity(
                agent_name=self.agent_name,
                activity="Initializing financial analysis",
                thread_id=thread_id,
                details=f"Analyzing research data for {self.company_name}"
            )

            # Perform analysis with streaming
            analysis_results = await self._perform_financial_analysis(
                research_data, thread_id, context
            )

            # Update progress
            await self._update_phase_state(thread_id, "analysis", 75.0)

            # Generate insights and recommendations
            insights = await self._generate_insights(analysis_results, thread_id)

            # Final state update
            await self._update_phase_state(thread_id, "analysis", 100.0)

            # Stream completion
            await self.event_stream.stream_text_message(
                content=f"✅ Analysis phase completed for {self.company_name}",
                thread_id=thread_id,
                role="assistant",
                metadata={
                    "phase": "analysis",
                    "status": "completed",
                    "agent_name": self.agent_name
                }
            )

            return {
                "status": "success",
                "agent": self.agent_name,
                "phase": "analysis",
                "company": self.company_name,
                "analysis": analysis_results,
                "insights": insights,
                "metadata": {
                    "enhanced_mode": self.enhanced,
                    "analysis_framework": "comprehensive" if self.enhanced else "standard"
                }
            }

        except Exception as e:
            # Handle errors with streaming
            await self._handle_workflow_error(e, thread_id, "analysis")
            raise

    async def _perform_financial_analysis(
        self,
        research_data: Dict[str, Any],
        thread_id: str,
        context: Optional[Any] = None
    ) -> Dict[str, Any]:
        """Perform comprehensive financial analysis with streaming."""

        await self.event_stream.stream_agent_activity(
            agent_name=self.agent_name,
            activity="Analyzing stock performance",
            thread_id=thread_id,
            details="Evaluating price movements and market performance"
        )

        analysis = {
            "stock_performance": await self._analyze_stock_performance(research_data, thread_id),
            "earnings_analysis": await self._analyze_earnings(research_data, thread_id),
            "market_context": await self._analyze_market_context(research_data, thread_id),
            "risk_assessment": await self._assess_risks(research_data, thread_id) if self.enhanced else None
        }

        # Update progress incrementally
        await self._update_phase_state(thread_id, "analysis", 50.0)

        return analysis

    async def _analyze_stock_performance(
        self,
        research_data: Dict[str, Any],
        thread_id: str
    ) -> Dict[str, Any]:
        """Analyze stock performance data."""

        stock_data = research_data.get("stock_data", [])

        await self.event_stream.stream_text_message(
            content="📈 Analyzing stock performance metrics...",
            thread_id=thread_id,
            role="assistant"
        )

        # Extract and analyze stock data
        performance = {
            "current_price": None,
            "price_change": None,
            "percentage_change": None,
            "volume_analysis": None,
            "trend_analysis": "neutral"
        }

        # Process stock data (simplified for demo)
        if stock_data:
            performance["data_available"] = True
            performance["sources"] = len(stock_data)
        else:
            performance["data_available"] = False

        return performance

    async def _analyze_earnings(
        self,
        research_data: Dict[str, Any],
        thread_id: str
    ) -> Dict[str, Any]:
        """Analyze earnings data."""

        earnings_data = research_data.get("earnings_data", [])

        await self.event_stream.stream_text_message(
            content="💰 Analyzing earnings performance...",
            thread_id=thread_id,
            role="assistant"
        )

        earnings = {
            "beat_expectations": None,
            "eps_actual": None,
            "eps_expected": None,
            "revenue_growth": None,
            "guidance": None
        }

        # Process earnings data (simplified for demo)
        if earnings_data:
            earnings["data_available"] = True
            earnings["sources"] = len(earnings_data)
        else:
            earnings["data_available"] = False

        return earnings

    async def _analyze_market_context(
        self,
        research_data: Dict[str, Any],
        thread_id: str
    ) -> Dict[str, Any]:
        """Analyze market context and news."""

        news_data = research_data.get("news_data", [])
        analyst_data = research_data.get("analyst_data", [])

        await self.event_stream.stream_text_message(
            content="🌐 Analyzing market context and sentiment...",
            thread_id=thread_id,
            role="assistant"
        )

        context = {
            "recent_news": len(news_data),
            "analyst_coverage": len(analyst_data),
            "sentiment": "neutral",
            "key_developments": []
        }

        return context

    async def _assess_risks(
        self,
        research_data: Dict[str, Any],
        thread_id: str
    ) -> Dict[str, Any]:
        """Assess investment risks (enhanced mode only)."""

        await self.event_stream.stream_text_message(
            content="⚠️ Conducting risk assessment...",
            thread_id=thread_id,
            role="assistant"
        )

        risks = {
            "business_risks": [],
            "financial_risks": [],
            "market_risks": [],
            "overall_risk_level": "moderate"
        }

        return risks

    async def _generate_insights(
        self,
        analysis_results: Dict[str, Any],
        thread_id: str
    ) -> Dict[str, Any]:
        """Generate investment insights and recommendations."""

        await self.event_stream.stream_agent_activity(
            agent_name=self.agent_name,
            activity="Generating investment insights",
            thread_id=thread_id,
            details="Synthesizing analysis into actionable recommendations"
        )

        insights = {
            "strengths": [
                "Strong market position",
                "Consistent earnings growth"
            ],
            "concerns": [
                "Market volatility",
                "Competitive pressure"
            ],
            "recommendation": "HOLD",
            "confidence_level": "moderate",
            "key_metrics": analysis_results
        }

        # Stream insights
        await self.event_stream.stream_text_message(
            content="💡 Generated investment insights and recommendations",
            thread_id=thread_id,
            role="assistant",
            metadata={
                "insights": insights,
                "agent_name": self.agent_name
            }
        )

        return insights

    def get_agent_info(self) -> Dict[str, Any]:
        """Get agent information for AG-UI display."""
        return {
            "name": self.agent_name,
            "type": "analysis",
            "company": self.company_name,
            "enhanced": self.enhanced,
            "capabilities": [
                "Stock performance analysis",
                "Earnings analysis",
                "Market context evaluation",
                "Risk assessment" if self.enhanced else None,
                "Investment recommendations"
            ],
            "tools": ["fetch"],
            "phase": self.phase_name
        }


class AGUIReportWriter(AbstractAgent):
    """
    AG-UI enabled report writer agent with real-time streaming capabilities.

    Extends the existing report writer functionality with AG-UI protocol support
    for streaming report generation progress and final results.
    """

    def __init__(
        self,
        company_name: str,
        output_path: str,
        state_manager: AGUIStateManager,
        event_stream: FinancialAnalysisEventStream,
        tool_bridge: MCPToAGUIToolBridge
    ):
        """
        Initialize AG-UI report writer agent.

        Args:
            company_name: Company for the report
            output_path: File path for report output
            state_manager: AG-UI state manager
            event_stream: Event streaming infrastructure
            tool_bridge: MCP to AG-UI tool bridge
        """
        self.company_name = company_name
        self.output_path = output_path

        # Create underlying MCP agent
        self.mcp_agent = create_report_writer(company_name, output_path)

        # Initialize parent with MCP agent and name
        super().__init__(self.mcp_agent, "Report Writer")

        # Store AG-UI components
        self.state_manager = state_manager
        self.event_stream = event_stream
        self.tool_bridge = tool_bridge

        # Agent configuration
        self.agent_name = "Report Writer"
        self.phase_name = "Report Generation"

    async def execute_agent_workflow(
        self,
        thread_id: str,
        research_data: Dict[str, Any],
        analysis_data: Dict[str, Any],
        context: Optional[Any] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Execute report generation workflow with AG-UI streaming.

        Args:
            thread_id: AG-UI thread ID
            research_data: Research results from research agent
            analysis_data: Analysis results from analyst agent
            context: MCP context for tool execution
            **kwargs: Additional workflow parameters

        Returns:
            Report generation results with metadata
        """
        try:
            # Update state to report generation phase
            await self._update_phase_state(thread_id, "reporting", 0.0)

            # Stream phase start
            await self.event_stream.stream_analysis_phase(
                phase_name=self.phase_name,
                phase_description=f"Generating comprehensive financial report for {self.company_name}",
                thread_id=thread_id,
                progress=0.0
            )

            # Stream agent activity
            await self.event_stream.stream_agent_activity(
                agent_name=self.agent_name,
                activity="Initializing report generation",
                thread_id=thread_id,
                details=f"Preparing to generate report for {self.company_name}"
            )

            # Generate report sections with streaming
            report_content = await self._generate_report_sections(
                research_data, analysis_data, thread_id, context
            )

            # Update progress
            await self._update_phase_state(thread_id, "reporting", 75.0)

            # Save report and finalize
            report_path = await self._save_report(report_content, thread_id)

            # Final state update
            await self._update_phase_state(thread_id, "reporting", 100.0)

            # Stream completion with results
            await self.event_stream.stream_results(
                results={
                    "report_path": report_path,
                    "company": self.company_name,
                    "sections": list(report_content.keys()),
                    "word_count": sum(len(content.split()) for content in report_content.values())
                },
                thread_id=thread_id,
                result_type="financial_report"
            )

            return {
                "status": "success",
                "agent": self.agent_name,
                "phase": "reporting",
                "company": self.company_name,
                "report_path": report_path,
                "report_content": report_content,
                "metadata": {
                    "sections_generated": len(report_content),
                    "output_format": "markdown",
                    "file_saved": True
                }
            }

        except Exception as e:
            # Handle errors with streaming
            await self._handle_workflow_error(e, thread_id, "reporting")
            raise

    async def _generate_report_sections(
        self,
        research_data: Dict[str, Any],
        analysis_data: Dict[str, Any],
        thread_id: str,
        context: Optional[Any] = None
    ) -> Dict[str, str]:
        """Generate report sections with streaming progress."""

        sections = {}
        section_names = [
            "Executive Summary",
            "Company Overview",
            "Financial Performance",
            "Market Analysis",
            "Investment Recommendation",
            "Risk Assessment",
            "Conclusion"
        ]

        total_sections = len(section_names)

        for i, section_name in enumerate(section_names):
            # Stream section generation
            await self.event_stream.stream_agent_activity(
                agent_name=self.agent_name,
                activity=f"Writing {section_name}",
                thread_id=thread_id,
                details=f"Generating content for {section_name} section"
            )

            # Generate section content
            section_content = await self._generate_section_content(
                section_name, research_data, analysis_data, thread_id
            )

            sections[section_name] = section_content

            # Update progress
            progress = ((i + 1) / total_sections) * 60.0  # Report generation is 60% of total
            await self._update_phase_state(thread_id, "reporting", progress)

            # Stream section completion
            await self.event_stream.stream_text_message(
                content=f"📝 Completed {section_name} section ({len(section_content.split())} words)",
                thread_id=thread_id,
                role="assistant"
            )

        return sections

    async def _generate_section_content(
        self,
        section_name: str,
        research_data: Dict[str, Any],
        analysis_data: Dict[str, Any],
        thread_id: str
    ) -> str:
        """Generate content for a specific report section using the underlying MCP agent."""

        # Use the underlying MCP report writer agent to generate real content
        # Format the research and analysis data for the agent
        formatted_data = self._format_data_for_agent(research_data, analysis_data)

        # Create a prompt for the specific section
        section_prompt = f"""
Based on the following research and analysis data for {self.company_name},
generate the {section_name} section of a professional financial report.

Research Data:
{formatted_data['research_summary']}

Analysis Data:
{formatted_data['analysis_summary']}

Please generate only the {section_name} section with appropriate markdown formatting.
Make it professional, data-driven, and include specific figures where available.
"""

        try:
            # Stream that we're generating content with the MCP agent
            await self.event_stream.stream_text_message(
                content=f"🤖 Using MCP agent to generate {section_name} content...",
                thread_id=thread_id,
                role="assistant"
            )

            # Use the underlying MCP agent to generate the content
            # The MCP agent has access to the research and analysis data
            result = await self.mcp_agent.process_with_base_agent(
                message=section_prompt,
                context=f"Research: {formatted_data['research_summary']}\nAnalysis: {formatted_data['analysis_summary']}"
            )

            # Extract the generated content
            if isinstance(result, dict) and 'content' in result:
                return result['content']
            elif isinstance(result, str):
                return result
            else:
                # Fallback to formatted content if MCP agent result is unexpected
                return self._generate_fallback_content(section_name, research_data, analysis_data)

        except Exception as e:
            # Log the error and fall back to formatted content
            await self.event_stream.stream_text_message(
                content=f"⚠️ MCP agent error for {section_name}, using fallback: {str(e)}",
                thread_id=thread_id,
                role="assistant"
            )
            return self._generate_fallback_content(section_name, research_data, analysis_data)

    def _format_data_for_agent(self, research_data: Dict[str, Any], analysis_data: Dict[str, Any]) -> Dict[str, str]:
        """Format research and analysis data for the MCP agent."""

        # Format research data summary
        research_summary = f"""
Research Summary for {self.company_name}:
- Total queries executed: {research_data.get('summary', {}).get('total_queries', 0)}
- Successful queries: {research_data.get('summary', {}).get('successful_queries', 0)}
- Stock data sources: {len(research_data.get('stock_data', []))}
- Earnings data sources: {len(research_data.get('earnings_data', []))}
- News sources: {len(research_data.get('news_data', []))}
- Analyst data sources: {len(research_data.get('analyst_data', []))}
"""

        # Format analysis data summary
        analysis_summary = f"""
Analysis Summary for {self.company_name}:
- Stock performance analysis: {analysis_data.get('analysis', {}).get('stock_performance', {})}
- Earnings analysis: {analysis_data.get('analysis', {}).get('earnings_analysis', {})}
- Market context: {analysis_data.get('analysis', {}).get('market_context', {})}
- Risk assessment: {analysis_data.get('analysis', {}).get('risk_assessment', 'Not available')}
"""

        return {
            'research_summary': research_summary,
            'analysis_summary': analysis_summary
        }

    def _generate_fallback_content(self, section_name: str, research_data: Dict[str, Any], analysis_data: Dict[str, Any]) -> str:
        """Generate fallback content when MCP agent is not available."""

        if section_name == "Executive Summary":
            return f"""# Executive Summary

This report provides a comprehensive financial analysis of {self.company_name} based on current market data and recent performance metrics.

**Key Findings:**
- Stock performance analysis completed
- Earnings data evaluated
- Market context assessed
- Investment recommendation provided

**Recommendation:** {analysis_data.get('insights', {}).get('recommendation', 'HOLD')}
"""

        elif section_name == "Company Overview":
            return f"""# Company Overview

{self.company_name} is analyzed based on the latest available financial data and market information.

**Data Sources:**
- Stock price data: {len(research_data.get('stock_data', []))} sources
- Earnings information: {len(research_data.get('earnings_data', []))} sources
- Market news: {len(research_data.get('news_data', []))} sources
"""

        elif section_name == "Financial Performance":
            stock_analysis = analysis_data.get('analysis', {}).get('stock_performance', {})
            earnings_analysis = analysis_data.get('analysis', {}).get('earnings_analysis', {})

            return f"""# Financial Performance

## Stock Performance
- Data availability: {stock_analysis.get('data_available', 'Unknown')}
- Trend analysis: {stock_analysis.get('trend_analysis', 'Neutral')}

## Earnings Analysis
- Data availability: {earnings_analysis.get('data_available', 'Unknown')}
- Sources analyzed: {earnings_analysis.get('sources', 0)}
"""

        else:
            return f"""# {section_name}

Content for {section_name} section based on analysis of {self.company_name}.

This section would contain detailed analysis and insights relevant to {section_name.lower()}.
"""

    async def _save_report(
        self,
        report_content: Dict[str, str],
        thread_id: str
    ) -> str:
        """Save the generated report to file."""

        await self.event_stream.stream_agent_activity(
            agent_name=self.agent_name,
            activity="Saving report to file",
            thread_id=thread_id,
            details=f"Writing report to {self.output_path}"
        )

        # Combine all sections into full report
        full_report = f"""# Financial Analysis Report: {self.company_name}

Generated by AG-UI Financial Analyzer
Date: {asyncio.get_event_loop().time()}

---

"""

        for section_name, content in report_content.items():
            full_report += content + "\n\n---\n\n"

        # Save to file (simplified for demo)
        try:
            with open(self.output_path, 'w') as f:
                f.write(full_report)

            await self.event_stream.stream_text_message(
                content=f"💾 Report saved successfully to {self.output_path}",
                thread_id=thread_id,
                role="assistant"
            )

            return self.output_path

        except Exception as e:
            await self.event_stream.stream_text_message(
                content=f"❌ Failed to save report: {str(e)}",
                thread_id=thread_id,
                role="assistant"
            )
            raise

    def get_agent_info(self) -> Dict[str, Any]:
        """Get agent information for AG-UI display."""
        return {
            "name": self.agent_name,
            "type": "reporting",
            "company": self.company_name,
            "output_path": self.output_path,
            "capabilities": [
                "Report generation",
                "Content structuring",
                "File output",
                "Multi-section reports"
            ],
            "tools": ["filesystem"],
            "phase": self.phase_name
        }
