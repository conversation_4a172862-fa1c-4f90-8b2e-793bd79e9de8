"""
AG-UI Event Definitions
======================

Defines all AG-UI event types with proper schemas for financial analysis workflow.
Based on the technical specifications from the AG-UI integration plan.
"""

from typing import Dict, Any, List, Optional, Union
from datetime import datetime
from pydantic import BaseModel, Field
from enum import Enum
import uuid


class EventType(str, Enum):
    """Enumeration of AG-UI event types"""
    RUN_STARTED = "RUN_STARTED"
    RUN_FINISHED = "RUN_FINISHED"
    RUN_ERROR = "RUN_ERROR"
    TEXT_MESSAGE_START = "TEXT_MESSAGE_START"
    TEXT_MESSAGE_CONTENT = "TEXT_MESSAGE_CONTENT"
    TEXT_MESSAGE_END = "TEXT_MESSAGE_END"
    TOOL_CALL_START = "TOOL_CALL_START"
    TOOL_CALL_ARGS = "TOOL_CALL_ARGS"
    TOOL_CALL_END = "TOOL_CALL_END"
    STATE_SNAPSHOT = "STATE_SNAPSHOT"
    STATE_DELTA = "STATE_DELTA"


# Base event class alias for compatibility
BaseEvent = BaseModel


class AGUIEvent(BaseModel):
    """Base class for all AG-UI events"""
    type: str
    timestamp: datetime = Field(default_factory=datetime.now)
    threadId: str
    runId: Optional[str] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)


class RunStartedEvent(AGUIEvent):
    """Event emitted when a financial analysis run starts"""
    type: str = "RUN_STARTED"
    input: Dict[str, Any]
    
    @classmethod
    def create(cls, thread_id: str, run_id: str, input_data: Dict[str, Any], **metadata) -> "RunStartedEvent":
        return cls(
            threadId=thread_id,
            runId=run_id,
            input=input_data,
            metadata=metadata
        )


class RunFinishedEvent(AGUIEvent):
    """Event emitted when a financial analysis run completes successfully"""
    type: str = "RUN_FINISHED"
    output: Dict[str, Any]
    
    @classmethod
    def create(cls, thread_id: str, run_id: str, output_data: Dict[str, Any], **metadata) -> "RunFinishedEvent":
        return cls(
            threadId=thread_id,
            runId=run_id,
            output=output_data,
            metadata=metadata
        )


class RunErrorEvent(AGUIEvent):
    """Event emitted when a financial analysis run encounters an error"""
    type: str = "RUN_ERROR"
    error: Dict[str, Any]
    
    @classmethod
    def create(cls, thread_id: str, run_id: str, error_info: Dict[str, Any], **metadata) -> "RunErrorEvent":
        return cls(
            threadId=thread_id,
            runId=run_id,
            error=error_info,
            metadata=metadata
        )


class TextMessageStartEvent(AGUIEvent):
    """Event emitted when a text message starts streaming"""
    type: str = "TEXT_MESSAGE_START"
    messageId: str
    role: str = "assistant"
    
    @classmethod
    def create(cls, thread_id: str, message_id: str, role: str = "assistant", **metadata) -> "TextMessageStartEvent":
        return cls(
            threadId=thread_id,
            messageId=message_id,
            role=role,
            metadata=metadata
        )


class TextMessageContentEvent(AGUIEvent):
    """Event emitted for streaming text message content"""
    type: str = "TEXT_MESSAGE_CONTENT"
    messageId: str
    delta: str
    
    @classmethod
    def create(cls, thread_id: str, message_id: str, content_delta: str, **metadata) -> "TextMessageContentEvent":
        return cls(
            threadId=thread_id,
            messageId=message_id,
            delta=content_delta,
            metadata=metadata
        )


class TextMessageEndEvent(AGUIEvent):
    """Event emitted when a text message completes streaming"""
    type: str = "TEXT_MESSAGE_END"
    messageId: str
    
    @classmethod
    def create(cls, thread_id: str, message_id: str, **metadata) -> "TextMessageEndEvent":
        return cls(
            threadId=thread_id,
            messageId=message_id,
            metadata=metadata
        )


class ToolCallStartEvent(AGUIEvent):
    """Event emitted when a tool call starts"""
    type: str = "TOOL_CALL_START"
    toolCallId: str
    toolCallName: str
    parentMessageId: Optional[str] = None
    
    @classmethod
    def create(cls, thread_id: str, tool_call_id: str, tool_name: str, 
               parent_message_id: Optional[str] = None, **metadata) -> "ToolCallStartEvent":
        return cls(
            threadId=thread_id,
            toolCallId=tool_call_id,
            toolCallName=tool_name,
            parentMessageId=parent_message_id,
            metadata=metadata
        )


class ToolCallArgsEvent(AGUIEvent):
    """Event emitted for streaming tool call arguments"""
    type: str = "TOOL_CALL_ARGS"
    toolCallId: str
    delta: str
    
    @classmethod
    def create(cls, thread_id: str, tool_call_id: str, args_delta: str, **metadata) -> "ToolCallArgsEvent":
        return cls(
            threadId=thread_id,
            toolCallId=tool_call_id,
            delta=args_delta,
            metadata=metadata
        )


class ToolCallEndEvent(AGUIEvent):
    """Event emitted when a tool call completes"""
    type: str = "TOOL_CALL_END"
    toolCallId: str
    result: Optional[Dict[str, Any]] = None
    error: Optional[Dict[str, Any]] = None
    
    @classmethod
    def create(cls, thread_id: str, tool_call_id: str, result: Optional[Dict[str, Any]] = None,
               error: Optional[Dict[str, Any]] = None, **metadata) -> "ToolCallEndEvent":
        return cls(
            threadId=thread_id,
            toolCallId=tool_call_id,
            result=result,
            error=error,
            metadata=metadata
        )


class StateSnapshotEvent(AGUIEvent):
    """Event emitted for complete state snapshots"""
    type: str = "STATE_SNAPSHOT"
    state: Dict[str, Any]
    
    @classmethod
    def create(cls, thread_id: str, state_data: Dict[str, Any], **metadata) -> "StateSnapshotEvent":
        return cls(
            threadId=thread_id,
            state=state_data,
            metadata=metadata
        )


class StateDeltaEvent(AGUIEvent):
    """Event emitted for incremental state changes using JSON Patch"""
    type: str = "STATE_DELTA"
    delta: List[Dict[str, Any]]  # JSON Patch operations
    
    @classmethod
    def create(cls, thread_id: str, patch_operations: List[Dict[str, Any]], **metadata) -> "StateDeltaEvent":
        return cls(
            threadId=thread_id,
            delta=patch_operations,
            metadata=metadata
        )


# Event type mapping for easy access
EVENT_TYPES = {
    "RUN_STARTED": RunStartedEvent,
    "RUN_FINISHED": RunFinishedEvent,
    "RUN_ERROR": RunErrorEvent,
    "TEXT_MESSAGE_START": TextMessageStartEvent,
    "TEXT_MESSAGE_CONTENT": TextMessageContentEvent,
    "TEXT_MESSAGE_END": TextMessageEndEvent,
    "TOOL_CALL_START": ToolCallStartEvent,
    "TOOL_CALL_ARGS": ToolCallArgsEvent,
    "TOOL_CALL_END": ToolCallEndEvent,
    "STATE_SNAPSHOT": StateSnapshotEvent,
    "STATE_DELTA": StateDeltaEvent,
}


def create_event(event_type: str, **kwargs) -> AGUIEvent:
    """
    Factory function to create events by type name.
    
    Args:
        event_type: The type of event to create
        **kwargs: Event-specific parameters
        
    Returns:
        AGUIEvent instance of the specified type
        
    Raises:
        ValueError: If event_type is not recognized
    """
    if event_type not in EVENT_TYPES:
        raise ValueError(f"Unknown event type: {event_type}")
    
    event_class = EVENT_TYPES[event_type]
    return event_class(**kwargs)


def generate_message_id() -> str:
    """Generate a unique message ID"""
    return f"msg_{uuid.uuid4().hex[:12]}"


def generate_tool_call_id() -> str:
    """Generate a unique tool call ID"""
    return f"tool_{uuid.uuid4().hex[:12]}"


class EventEncoder:
    """Simple event encoder for AG-UI events"""

    def __init__(self, accept: str = "text/event-stream"):
        self.accept = accept
        self.content_type = "text/event-stream" if "event-stream" in accept else "application/json"

    def encode(self, event: AGUIEvent) -> str:
        """Encode an event for streaming"""
        import json

        if self.content_type == "text/event-stream":
            # Server-Sent Events format
            event_data = event.model_dump() if hasattr(event, 'model_dump') else event.dict()
            json_data = json.dumps(event_data)
            return f"data: {json_data}\n\n"
        else:
            # JSON format
            event_data = event.model_dump() if hasattr(event, 'model_dump') else event.dict()
            return json.dumps(event_data) + "\n"

    def get_content_type(self) -> str:
        """Get the content type for the response"""
        return self.content_type


# Additional types for compatibility with plan files
class Message(BaseModel):
    """Message type for AG-UI compatibility"""
    content: str
    role: str = "user"


class Tool(BaseModel):
    """Tool type for AG-UI compatibility"""
    name: str
    description: str
    parameters: Dict[str, Any] = Field(default_factory=dict)


class Context(BaseModel):
    """Context type for AG-UI compatibility"""
    description: str
    value: Any


class RunAgentInput(BaseModel):
    """Input type for running AG-UI agents"""
    thread_id: str
    messages: List[Message] = Field(default_factory=list)
    tools: List[Tool] = Field(default_factory=list)
    context: List[Context] = Field(default_factory=list)
