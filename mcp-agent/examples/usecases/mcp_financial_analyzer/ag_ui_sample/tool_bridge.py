"""
MCP to AG-UI Tool Bridge
========================

Bridges MCP tool calls to AG-UI streaming events, providing real-time
tool execution feedback for financial analysis operations.
"""

import json
import asyncio
from typing import Dict, Any, Optional, Callable, List
from datetime import datetime
from .events import (
    ToolCallStartEvent,
    ToolCallArgsEvent, 
    ToolCallEndEvent,
    generate_tool_call_id
)


class MCPToAGUIToolBridge:
    """
    Bridges MCP tool execution to AG-UI streaming events.
    
    Maps MCP tools to AG-UI tool call events while preserving functionality
    and adding streaming progress updates.
    """
    
    def __init__(self):
        """Initialize tool bridge with financial analysis tool mappings"""
        self.tool_mappings = self._get_financial_tool_mappings()
        self.active_tool_calls: Dict[str, Dict[str, Any]] = {}
    
    def _get_financial_tool_mappings(self) -> Dict[str, str]:
        """
        Get mapping from MCP tool names to AG-UI tool names.
        
        Returns:
            Dictionary mapping MCP tools to AG-UI tool names
        """
        return {
            "g-search": "search_financial_data",
            "fetch": "fetch_web_content", 
            "filesystem_read": "read_file",
            "filesystem_write": "write_file",
            "filesystem_list": "list_files"
        }
    
    async def execute_mcp_tool_with_agui_events(
        self,
        tool_name: str,
        tool_args: Dict[str, Any],
        observer: Callable,
        thread_id: str,
        parent_message_id: Optional[str] = None,
        context: Optional[Any] = None
    ) -> Any:
        """
        Execute MCP tool with AG-UI event streaming.
        
        Args:
            tool_name: MCP tool name
            tool_args: Tool arguments
            observer: Event observer function
            thread_id: Thread ID for events
            parent_message_id: Optional parent message ID
            context: Optional MCP context for tool execution
            
        Returns:
            Tool execution result
        """
        tool_call_id = generate_tool_call_id()
        agui_tool_name = self.tool_mappings.get(tool_name, tool_name)
        
        # Store tool call info
        self.active_tool_calls[tool_call_id] = {
            "mcp_tool_name": tool_name,
            "agui_tool_name": agui_tool_name,
            "args": tool_args,
            "start_time": datetime.now(),
            "thread_id": thread_id
        }
        
        try:
            # Emit TOOL_CALL_START event
            start_event = ToolCallStartEvent.create(
                thread_id=thread_id,
                tool_call_id=tool_call_id,
                tool_name=agui_tool_name,
                parent_message_id=parent_message_id,
                mcp_tool_name=tool_name
            )
            observer(start_event)
            
            # Emit TOOL_CALL_ARGS event with arguments
            args_json = json.dumps(tool_args, indent=2)
            args_event = ToolCallArgsEvent.create(
                thread_id=thread_id,
                tool_call_id=tool_call_id,
                args_delta=args_json
            )
            observer(args_event)
            
            # Execute the actual MCP tool
            result = await self._execute_mcp_tool(tool_name, tool_args, context)
            
            # Emit TOOL_CALL_END event with result
            end_event = ToolCallEndEvent.create(
                thread_id=thread_id,
                tool_call_id=tool_call_id,
                result={
                    "success": True,
                    "data": result,
                    "execution_time": (datetime.now() - self.active_tool_calls[tool_call_id]["start_time"]).total_seconds()
                }
            )
            observer(end_event)
            
            return result
            
        except Exception as e:
            # Emit TOOL_CALL_END event with error
            error_event = ToolCallEndEvent.create(
                thread_id=thread_id,
                tool_call_id=tool_call_id,
                error={
                    "type": type(e).__name__,
                    "message": str(e),
                    "tool_name": tool_name,
                    "args": tool_args
                }
            )
            observer(error_event)
            raise
            
        finally:
            # Clean up active tool call
            if tool_call_id in self.active_tool_calls:
                del self.active_tool_calls[tool_call_id]
    
    async def _execute_mcp_tool(
        self,
        tool_name: str,
        tool_args: Dict[str, Any],
        context: Optional[Any] = None
    ) -> Any:
        """
        Execute the actual MCP tool.
        
        Args:
            tool_name: MCP tool name
            tool_args: Tool arguments
            context: MCP context for tool execution
            
        Returns:
            Tool execution result
        """
        if not context:
            # Fallback for testing - simulate tool execution
            return await self._simulate_tool_execution(tool_name, tool_args)
        
        # Execute actual MCP tool through context
        try:
            # Check for mcp-agent style context with executor
            if hasattr(context, 'executor') and context.executor:
                from mcp_agent.agents.tasks import CallToolRequest

                # Create call tool request
                request = CallToolRequest(
                    agent_name="ag_ui_agent",
                    name=tool_name,
                    arguments=tool_args,
                    server_name=None  # Let the executor determine the server
                )

                # Execute through the executor
                result = await context.executor.execute(
                    context.task_registry.call_tool_task,
                    request
                )

                # Extract text content from result
                if hasattr(result, 'content') and result.content:
                    return '\n'.join([
                        content.text for content in result.content
                        if hasattr(content, 'text')
                    ])
                else:
                    return str(result)

            # Check for FastMCP style context with call_tool method
            elif hasattr(context, 'call_tool'):
                result = await context.call_tool(tool_name, tool_args)
                if isinstance(result, list):
                    return '\n'.join([
                        content.text for content in result
                        if hasattr(content, 'text')
                    ])
                return str(result)

            # Check for tools dictionary
            elif hasattr(context, 'tools') and tool_name in context.tools:
                tool = context.tools[tool_name]
                return await tool(**tool_args)
            else:
                raise ValueError(f"Tool {tool_name} not found in context. Context type: {type(context)}")
        except Exception as e:
            raise Exception(f"MCP tool execution failed: {str(e)}")
    
    async def _simulate_tool_execution(self, tool_name: str, tool_args: Dict[str, Any]) -> str:
        """
        Simulate tool execution for testing purposes.
        
        Args:
            tool_name: Tool name
            tool_args: Tool arguments
            
        Returns:
            Simulated result
        """
        # Add small delay to simulate real tool execution
        await asyncio.sleep(0.1)
        
        if tool_name == "g-search":
            query = tool_args.get("query", "")
            return f"Search results for: {query}\n\nMock financial data and news results..."
        
        elif tool_name == "fetch":
            url = tool_args.get("url", "")
            return f"Fetched content from: {url}\n\nMock web content..."
        
        elif tool_name.startswith("filesystem_"):
            operation = tool_name.replace("filesystem_", "")
            if operation == "read":
                return f"Mock file content from: {tool_args.get('path', 'unknown')}"
            elif operation == "write":
                return f"Successfully wrote to: {tool_args.get('path', 'unknown')}"
            elif operation == "list":
                return "Mock directory listing..."
        
        return f"Mock result for {tool_name} with args: {tool_args}"
    
    def get_active_tool_calls(self) -> Dict[str, Dict[str, Any]]:
        """Get information about currently active tool calls"""
        return self.active_tool_calls.copy()
    
    def get_tool_mapping(self, mcp_tool_name: str) -> str:
        """
        Get AG-UI tool name for MCP tool.
        
        Args:
            mcp_tool_name: MCP tool name
            
        Returns:
            AG-UI tool name
        """
        return self.tool_mappings.get(mcp_tool_name, mcp_tool_name)
    
    def add_tool_mapping(self, mcp_tool_name: str, agui_tool_name: str):
        """
        Add custom tool mapping.
        
        Args:
            mcp_tool_name: MCP tool name
            agui_tool_name: AG-UI tool name
        """
        self.tool_mappings[mcp_tool_name] = agui_tool_name
    
    def get_financial_analysis_tools(self) -> List[Dict[str, Any]]:
        """
        Get list of available financial analysis tools.
        
        Returns:
            List of tool definitions for AG-UI
        """
        return [
            {
                "name": "search_financial_data",
                "description": "Search for financial data, stock prices, and market information",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "query": {
                            "type": "string",
                            "description": "Search query for financial information"
                        },
                        "search_type": {
                            "type": "string", 
                            "enum": ["general", "stock_price", "earnings", "news"],
                            "description": "Type of financial search to perform"
                        }
                    },
                    "required": ["query"]
                }
            },
            {
                "name": "fetch_web_content",
                "description": "Fetch content from financial websites and reports",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "url": {
                            "type": "string",
                            "description": "URL to fetch content from"
                        }
                    },
                    "required": ["url"]
                }
            },
            {
                "name": "write_file",
                "description": "Write financial analysis report to file",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "path": {
                            "type": "string",
                            "description": "File path to write to"
                        },
                        "content": {
                            "type": "string",
                            "description": "Content to write to file"
                        }
                    },
                    "required": ["path", "content"]
                }
            }
        ]
