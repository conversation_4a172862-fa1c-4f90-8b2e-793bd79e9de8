#!/usr/bin/env python3
"""
Comprehensive Test Suite for AG-UI MCP Tool Execution
====================================================

Tests to verify and fix the tool execution issue where AG-UI mode falls back
to simulated tool execution instead of using real MCP tools.
"""

import sys
import os
import asyncio
import json
import tempfile
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from typing import Dict, Any, List, Optional
from datetime import datetime

# Add parent directories to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

# Import AG-UI components
from ag_ui.tool_bridge import MCPToAGUIToolBridge
from ag_ui.agent_wrappers import AGUIResearchAgent
from ag_ui.state_manager import <PERSON>UI<PERSON>tate<PERSON>anager
from ag_ui.event_stream import FinancialAnalysisEventStream

class MockMCPContext:
    """Mock MCP context that simulates real MCP tool execution."""

    def __init__(self, simulate_real_tools: bool = True):
        self.simulate_real_tools = simulate_real_tools
        self.tool_calls = []

    async def call_tool(self, tool_name: str, tool_args: Dict[str, Any]) -> str:
        """Simulate real MCP tool execution with realistic responses."""
        self.tool_calls.append({
            "tool_name": tool_name,
            "args": tool_args,
            "timestamp": datetime.now().isoformat()
        })

        if tool_name == "g-search":
            query = tool_args.get("query", "")
            # Return realistic search results, not mock data
            return f"""Search Results for: {query}

1. Tesla Stock Price Today - Yahoo Finance
   Current Price: $248.50 (+2.3%)
   Market Cap: $789.2B

2. Tesla Q3 2024 Earnings Report - SEC Filing
   Revenue: $25.18B (+8% YoY)
   Net Income: $2.17B

3. Tesla Latest News - Reuters
   Tesla announces new Gigafactory expansion
   Production targets increased for 2025"""

        elif tool_name == "fetch":
            url = tool_args.get("url", "")
            return f"""Fetched content from: {url}

Tesla Inc. (TSLA) Financial Data:
- Stock Price: $248.50
- 52-Week High: $299.29
- 52-Week Low: $138.80
- P/E Ratio: 65.4
- Market Cap: $789.2B
- Revenue (TTM): $96.77B"""

        return f"Real MCP tool result for {tool_name}"

class TestMCPToolExecution:
    """Test suite for MCP tool execution in AG-UI mode."""

    def __init__(self):
        self.test_results = []
        self.simulation_calls = []

    async def test_tool_bridge_with_valid_context(self):
        """Test that tool bridge uses real MCP tools when valid context is provided."""
        print("🧪 Testing tool bridge with valid MCP context...")

        # Create mock context
        mock_context = MockMCPContext(simulate_real_tools=True)

        # Create tool bridge
        tool_bridge = MCPToAGUIToolBridge()

        # Mock observer
        events_received = []
        def mock_observer(event):
            events_received.append(event)

        # Patch _simulate_tool_execution to track if it's called
        with patch.object(tool_bridge, '_simulate_tool_execution',
                         side_effect=Exception("Simulation should not be called!")) as mock_sim:

            try:
                # Execute tool with valid context
                result = await tool_bridge.execute_mcp_tool_with_agui_events(
                    tool_name="g-search",
                    tool_args={"query": "Tesla stock price"},
                    observer=mock_observer,
                    thread_id="test-thread-1",
                    context=mock_context
                )

                # Verify real tool was called, not simulation
                assert mock_sim.call_count == 0, "Simulation was called when real context was available!"
                assert "Tesla Stock Price Today" in result, "Real search results not returned"
                assert "Mock financial data" not in result, "Mock data returned instead of real data"

                # Verify tool call was logged in context
                assert len(mock_context.tool_calls) == 1
                assert mock_context.tool_calls[0]["tool_name"] == "g-search"

                # Verify AG-UI events were emitted
                assert len(events_received) >= 3, "Expected TOOL_CALL_START, ARGS, and END events"

                print("✅ PASS: Tool bridge correctly used real MCP tools")
                return True

            except Exception as e:
                print(f"❌ FAIL: {str(e)}")
                return False

    async def test_tool_bridge_without_context(self):
        """Test that tool bridge falls back to simulation when no context is provided."""
        print("🧪 Testing tool bridge without MCP context...")

        tool_bridge = MCPToAGUIToolBridge()
        events_received = []

        def mock_observer(event):
            events_received.append(event)

        # Execute tool without context
        result = await tool_bridge.execute_mcp_tool_with_agui_events(
            tool_name="g-search",
            tool_args={"query": "Tesla stock price"},
            observer=mock_observer,
            thread_id="test-thread-2",
            context=None  # No context provided
        )

        # Verify simulation was used
        assert "Mock financial data" in result, "Expected mock data when no context provided"
        assert "Tesla Stock Price Today" not in result, "Real data returned when simulation expected"

        print("✅ PASS: Tool bridge correctly fell back to simulation without context")
        return True

    async def test_agui_research_agent_context_passing(self):
        """Test that AG-UI research agent properly passes context to tool bridge."""
        print("🧪 Testing AG-UI research agent context passing...")

        # Create mock context
        mock_context = MockMCPContext(simulate_real_tools=True)

        # Create AG-UI components
        state_manager = AGUIStateManager()
        event_stream = FinancialAnalysisEventStream()

        # Create research agent
        research_agent = AGUIResearchAgent(
            company_name="Tesla",
            state_manager=state_manager,
            event_stream=event_stream,
            tool_bridge=MCPToAGUIToolBridge(),
            enhanced=True
        )

        # Patch the tool bridge to track context passing
        original_execute = research_agent.tool_bridge.execute_mcp_tool_with_agui_events
        context_received = []

        async def track_context_execute(*args, **kwargs):
            context_received.append(kwargs.get('context'))
            return await original_execute(*args, **kwargs)

        research_agent.tool_bridge.execute_mcp_tool_with_agui_events = track_context_execute

        # Execute research queries
        try:
            results = await research_agent._execute_research_queries(
                thread_id="test-thread-3",
                context=mock_context
            )

            # Debug output
            print(f"Debug: context_received = {context_received}")
            print(f"Debug: mock_context.tool_calls = {mock_context.tool_calls}")
            print(f"Debug: results = {results}")

            # Verify context was passed to tool bridge
            if len(context_received) == 0:
                print("❌ FAIL: No tool calls made during research")
                return False

            if not all(ctx is not None for ctx in context_received):
                print("❌ FAIL: Context not passed to tool bridge")
                return False

            if not all(ctx == mock_context for ctx in context_received):
                print("❌ FAIL: Wrong context passed")
                return False

            # Verify real tool execution occurred
            if len(mock_context.tool_calls) == 0:
                print("❌ FAIL: No real tool calls made")
                return False

            print("✅ PASS: Research agent correctly passed context to tool bridge")
            return True

        except Exception as e:
            print(f"❌ FAIL: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

    async def test_jsonl_logging_with_real_tools(self):
        """Test that real tool execution generates proper JSONL logs."""
        print("🧪 Testing JSONL logging with real tool execution...")

        mock_context = MockMCPContext(simulate_real_tools=True)
        tool_bridge = MCPToAGUIToolBridge()

        # Capture all events
        logged_events = []

        def capture_events(event):
            logged_events.append(event)
            # Simulate JSONL logging
            event_dict = event.model_dump() if hasattr(event, 'model_dump') else event
            jsonl_entry = {
                "level": "INFO",
                "timestamp": datetime.now().isoformat(),
                "namespace": "mcp_agent.ag_ui.tool_bridge",
                "message": f"Tool execution: {event_dict.get('type', 'unknown')}",
                "data": str(event_dict)  # Convert to string to avoid JSON serialization issues
            }
            print(f"JSONL: {json.dumps(jsonl_entry)}")

        # Execute multiple tools
        tools_to_test = [
            ("g-search", {"query": "Tesla earnings Q3 2024"}),
            ("fetch", {"url": "https://example.com/tesla-data"}),
            ("g-search", {"query": "Tesla stock price current"})
        ]

        for tool_name, tool_args in tools_to_test:
            await tool_bridge.execute_mcp_tool_with_agui_events(
                tool_name=tool_name,
                tool_args=tool_args,
                observer=capture_events,
                thread_id="test-thread-4",
                context=mock_context
            )

        # Verify proper event logging
        expected_events = len(tools_to_test) * 3  # START, ARGS, END for each tool
        assert len(logged_events) >= expected_events, f"Expected {expected_events} events, got {len(logged_events)}"

        # Verify event types
        event_types = [event.type if hasattr(event, 'type') else event.get('type', 'unknown') for event in logged_events]
        assert 'TOOL_CALL_START' in event_types, "Missing TOOL_CALL_START events"
        assert 'TOOL_CALL_ARGS' in event_types, "Missing TOOL_CALL_ARGS events"
        assert 'TOOL_CALL_END' in event_types, "Missing TOOL_CALL_END events"

        print("✅ PASS: Real tool execution generated proper JSONL logs")
        return True

    async def test_report_generation_with_real_data(self):
        """Test that reports contain real data when using actual MCP tools."""
        print("🧪 Testing report generation with real data...")

        mock_context = MockMCPContext(simulate_real_tools=True)

        # Simulate research data from real tools
        research_data = [
            {
                "query": "Tesla stock price today",
                "result": """Tesla Stock Price Today - Yahoo Finance
Current Price: $248.50 (+2.3%)
Market Cap: $789.2B
52-Week High: $299.29""",
                "status": "success"
            },
            {
                "query": "Tesla Q3 2024 earnings",
                "result": """Tesla Q3 2024 Earnings Report
Revenue: $25.18B (+8% YoY)
Net Income: $2.17B
EPS: $0.72""",
                "status": "success"
            }
        ]

        # Verify data contains real financial information
        for data in research_data:
            result = data["result"]

            # Check for real data indicators
            assert "$" in result, "No currency symbols found in financial data"
            assert any(char.isdigit() for char in result), "No numerical data found"
            assert "Mock" not in result, "Mock data found in real tool results"
            assert "Content for" not in result, "Template content found in results"

        # Verify timestamp format is correct (not corrupted like "14748147.67194754")
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        assert len(timestamp) == 15, "Timestamp format incorrect"
        assert timestamp.isdigit() == False, "Timestamp should contain underscores"

        print("✅ PASS: Report generation uses real financial data")
        return True

    async def run_all_tests(self):
        """Run all test cases and report results."""
        print("=" * 80)
        print("🧪 AG-UI MCP Tool Execution Test Suite")
        print("=" * 80)
        print()

        test_methods = [
            self.test_tool_bridge_with_valid_context,
            self.test_tool_bridge_without_context,
            self.test_agui_research_agent_context_passing,
            self.test_jsonl_logging_with_real_tools,
            self.test_report_generation_with_real_data
        ]

        passed = 0
        failed = 0

        for test_method in test_methods:
            try:
                result = await test_method()
                if result:
                    passed += 1
                else:
                    failed += 1
            except Exception as e:
                print(f"❌ FAIL: {test_method.__name__} - {str(e)}")
                failed += 1
            print()

        print("=" * 80)
        print(f"📊 Test Results: {passed} passed, {failed} failed")

        if failed > 0:
            print("❌ Some tests failed - AG-UI tool execution needs fixes")
            return False
        else:
            print("✅ All tests passed - AG-UI tool execution working correctly")
            return True

async def main():
    """Main function to run the test suite."""
    if len(sys.argv) > 1 and sys.argv[1] == "fix":
        print("🔧 Running tests and applying fixes...")
        await run_tests_and_fix()
    else:
        print("🧪 Running AG-UI MCP Tool Execution Tests...")
        test_suite = TestMCPToolExecution()
        success = await test_suite.run_all_tests()

        if not success:
            print("\n💡 To apply fixes, run: python test-search.py fix")

        return success

async def run_tests_and_fix():
    """Run tests and apply fixes to the AG-UI implementation."""
    print("🔧 Test-Driven Development: Running tests and applying fixes...")

    # First, run tests to confirm they fail
    test_suite = TestMCPToolExecution()
    print("📋 Step 1: Running initial tests (expecting failures)...")
    initial_success = await test_suite.run_all_tests()

    if initial_success:
        print("✅ Tests already pass - no fixes needed!")
        return

    print("\n🔧 Step 2: Applying fixes to AG-UI implementation...")
    await apply_context_passing_fixes()

    print("\n📋 Step 3: Re-running tests after fixes...")
    final_success = await test_suite.run_all_tests()

    if final_success:
        print("🎉 All tests now pass! AG-UI tool execution fixed.")
    else:
        print("❌ Some tests still failing - additional fixes needed.")

async def apply_context_passing_fixes():
    """Apply fixes to ensure proper context passing in AG-UI mode."""
    print("🔧 Applying context passing fixes...")

    # This would contain the actual fixes to the codebase
    # For now, we'll print what needs to be fixed

    fixes_needed = [
        "1. Update AGUIResearchAgent._execute_research_queries to pass context",
        "2. Update MCPToAGUIToolBridge to validate context before simulation",
        "3. Ensure all AG-UI agents receive and pass MCP context properly",
        "4. Add context validation logging for debugging"
    ]

    for fix in fixes_needed:
        print(f"   {fix}")

    print("   (Fixes would be applied to actual source files)")

if __name__ == "__main__":
    asyncio.run(main())
