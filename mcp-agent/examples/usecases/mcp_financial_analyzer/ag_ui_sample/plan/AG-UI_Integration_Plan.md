# AG-UI Protocol Integration Plan for MCP Financial Analyzer

## Executive Summary

This document outlines a comprehensive plan to integrate AG-UI protocol support into the existing MCP Financial Analyzer, enabling real-time streaming communication, enhanced user interaction, and standardized agent communication protocols.

## 1. Current State Analysis

### 1.1 Existing Architecture Overview

The MCP Financial Analyzer currently implements a sophisticated multi-agent orchestration system:

**Core Components:**
- **Main Application** (`main.py`): Entry point using MCPApp framework
- **Orchestrator**: Coordinates workflow between specialized agents
- **Agent Architecture**: Uses BaseAgentWrapper extending MCP Agent class
- **Specialized Agents**:
  - Research Agent: Google Search-powered financial data collection
  - Research Evaluator: Data quality assessment
  - Analyst Agent: Financial analysis and insights
  - Report Writer: Professional report generation

**Current Technology Stack:**
- **Framework**: mcp-agent with asyncio execution
- **Agent Enhancement**: atomic-agents integration via BaseAgentWrapper
- **LLM Integration**: VLLM and OpenAI support
- **Data Sources**: Google Search MCP, Fetch MCP, Filesystem MCP
- **Quality Control**: EvaluatorOptimizerLLM for iterative improvement

**Key Strengths:**
- Robust multi-agent orchestration
- Quality-driven research with feedback loops
- Structured data schemas for financial analysis
- Enhanced agent capabilities through atomic-agents
- Comprehensive logging and error handling

**Current Limitations for AG-UI Integration:**
- No real-time streaming communication
- Limited frontend interaction capabilities
- No standardized event-driven communication
- Missing tool call streaming support
- No state management for frontend synchronization

### 1.2 Dependencies Analysis

**Current Dependencies:**
```
mcp-agent
openai
anthropic
atomic-agents>=1.1.3
instructor
```

**MCP Servers:**
- g-search-mcp (Google Search)
- mcp-server-fetch (Web fetching)
- @modelcontextprotocol/server-filesystem (File operations)

## 2. AG-UI Integration Strategy

### 2.1 Component Selection Strategy

Based on AG-UI documentation analysis, the integration will utilize:

**Primary AG-UI Components:**
1. **AbstractAgent**: Base class for event processing, state management, and message history
2. **Custom Agent Implementation**: Extend AbstractAgent for financial analysis workflows
3. **Event System**: Implement streaming event-based communication
4. **Tool Integration**: Map existing MCP tools to AG-UI tool call events

**Integration Approach:**
- **Hybrid Architecture**: Maintain existing MCP orchestration while adding AG-UI streaming layer
- **Agent Wrapper Pattern**: Create AG-UI wrapper around existing BaseAgentWrapper
- **Event Bridge**: Convert MCP operations to AG-UI events for frontend streaming
- **State Synchronization**: Implement AG-UI state management for analysis context

### 2.2 Event Types Required

**Core Event Types for Financial Analysis:**

1. **Lifecycle Events:**
   - `RUN_STARTED`: Begin financial analysis workflow
   - `RUN_FINISHED`: Complete analysis with results
   - `RUN_ERROR`: Handle analysis errors gracefully

2. **Text Message Events:**
   - `TEXT_MESSAGE_START`: Begin streaming analysis results
   - `TEXT_MESSAGE_CONTENT`: Stream analysis content in chunks
   - `TEXT_MESSAGE_END`: Complete message streaming

3. **Tool Call Events:**
   - `TOOL_CALL_START`: Begin financial data collection
   - `TOOL_CALL_ARGS`: Stream search parameters and queries
   - `TOOL_CALL_END`: Complete data collection operations

4. **State Management Events:**
   - `STATE_SNAPSHOT`: Full analysis state for frontend sync
   - `STATE_DELTA`: Incremental updates during analysis

### 2.3 Financial Analysis Workflow Mapping

**Current Workflow → AG-UI Event Mapping:**

```
Research Phase:
MCP: research_agent.run() → AG-UI: TOOL_CALL_START/ARGS/END + TEXT_MESSAGE_*

Quality Control:
MCP: evaluator_optimizer.evaluate() → AG-UI: STATE_DELTA + TEXT_MESSAGE_*

Analysis Phase:
MCP: analyst_agent.run() → AG-UI: TEXT_MESSAGE_* + STATE_DELTA

Report Generation:
MCP: report_writer.run() → AG-UI: TOOL_CALL_* + TEXT_MESSAGE_* + STATE_SNAPSHOT
```

## 3. Implementation Steps

### 3.1 Phase 1: Foundation Setup

**Step 1: Install AG-UI Dependencies**
```bash
# Add to requirements.txt
@ag-ui/client  # or equivalent Python package
rxjs  # for Observable support
```

**Step 2: Create AG-UI Agent Base Class**
```python
# agents/ag_ui_financial_agent.py
from abc import abstractmethod
from typing import Observable
from ag_ui import AbstractAgent, EventType, BaseEvent

class AGUIFinancialAgent(AbstractAgent):
    """Base AG-UI agent for financial analysis workflows"""
    
    def __init__(self, mcp_agent_wrapper: BaseAgentWrapper):
        super().__init__()
        self.mcp_agent = mcp_agent_wrapper
        self.analysis_state = {}
    
    @abstractmethod
    def run(self, input: RunAgentInput) -> RunAgent:
        """Implement AG-UI streaming interface"""
        pass
```

**Step 3: Event Stream Infrastructure**
```python
# utils/event_stream.py
class FinancialAnalysisEventStream:
    """Manages AG-UI event streaming for financial analysis"""
    
    def emit_research_start(self, company_name: str):
        """Emit research phase start events"""
        
    def stream_analysis_content(self, content: str, message_id: str):
        """Stream analysis content in chunks"""
        
    def emit_state_update(self, analysis_state: dict):
        """Emit state management events"""
```

### 3.2 Phase 2: Agent Integration

**Step 4: Research Agent AG-UI Wrapper**
```python
# agents/ag_ui_research_agent.py
class AGUIResearchAgent(AGUIFinancialAgent):
    """AG-UI wrapper for research agent with streaming support"""
    
    def run(self, input: RunAgentInput) -> RunAgent:
        return lambda: Observable.create(self._execute_research)
    
    def _execute_research(self, observer):
        # Emit RUN_STARTED
        # Execute MCP research with streaming events
        # Emit TOOL_CALL_* events for search operations
        # Stream results via TEXT_MESSAGE_* events
        # Emit RUN_FINISHED
```

**Step 5: Analysis Agent AG-UI Integration**
```python
# agents/ag_ui_analyst_agent.py
class AGUIAnalystAgent(AGUIFinancialAgent):
    """AG-UI wrapper for financial analysis with real-time streaming"""
    
    def run(self, input: RunAgentInput) -> RunAgent:
        return lambda: Observable.create(self._execute_analysis)
    
    def _execute_analysis(self, observer):
        # Stream analysis process in real-time
        # Emit state updates for frontend synchronization
        # Provide progress indicators
```

### 3.3 Phase 3: Tool Integration

**Step 6: Financial Analysis Tools Definition**
```python
# tools/financial_tools.py
FINANCIAL_ANALYSIS_TOOLS = [
    {
        "name": "search_financial_data",
        "description": "Search for financial data using Google Search",
        "parameters": {
            "type": "object",
            "properties": {
                "company_name": {"type": "string"},
                "query_type": {"type": "string", "enum": ["stock_price", "earnings", "news"]},
                "time_period": {"type": "string"}
            }
        }
    },
    {
        "name": "analyze_financial_metrics",
        "description": "Analyze financial metrics and generate insights",
        "parameters": {
            "type": "object", 
            "properties": {
                "research_data": {"type": "string"},
                "analysis_type": {"type": "string"}
            }
        }
    },
    {
        "name": "generate_report",
        "description": "Generate comprehensive financial analysis report",
        "parameters": {
            "type": "object",
            "properties": {
                "analysis_data": {"type": "string"},
                "output_format": {"type": "string", "enum": ["markdown", "pdf", "html"]}
            }
        }
    }
]
```

**Step 7: Tool Execution Bridge**
```python
# utils/tool_bridge.py
class MCPToAGUIToolBridge:
    """Bridges MCP tool execution to AG-UI tool call events"""
    
    async def execute_tool(self, tool_name: str, args: dict, observer):
        # Emit TOOL_CALL_START
        # Execute corresponding MCP operation
        # Stream TOOL_CALL_ARGS with progress
        # Emit TOOL_CALL_END with results

### 3.4 Phase 4: State Management Implementation

**Step 8: Financial Analysis State Schema**
```python
# schemas/ag_ui_state_schemas.py
from pydantic import BaseModel
from typing import Optional, Dict, List

class FinancialAnalysisState(BaseModel):
    """State schema for AG-UI financial analysis"""
    company_name: str
    analysis_phase: str  # "research", "evaluation", "analysis", "reporting"
    research_data: Optional[Dict] = None
    analysis_results: Optional[Dict] = None
    quality_metrics: Optional[Dict] = None
    progress_percentage: float = 0.0
    current_operation: Optional[str] = None
    error_state: Optional[str] = None
```

**Step 9: State Management Implementation**
```python
# utils/state_manager.py
class AGUIStateManager:
    """Manages AG-UI state for financial analysis workflow"""

    def __init__(self):
        self.current_state = FinancialAnalysisState()

    def update_state(self, updates: Dict, observer):
        """Update state and emit STATE_DELTA event"""
        # Apply updates to current_state
        # Generate JSON Patch for incremental updates
        # Emit STATE_DELTA event

    def snapshot_state(self, observer):
        """Emit complete state snapshot"""
        # Emit STATE_SNAPSHOT event with full state
```

## 4. Technical Specifications

### 4.1 AG-UI Event Implementation Details

**Event Type Specifications:**

1. **RUN_STARTED Event**
```python
{
    "type": "RUN_STARTED",
    "threadId": "financial_analysis_thread",
    "runId": "analysis_run_001",
    "timestamp": "2025-07-01T10:00:00Z",
    "metadata": {
        "company_name": "Apple Inc.",
        "analysis_type": "comprehensive"
    }
}
```

2. **TEXT_MESSAGE_CONTENT Event**
```python
{
    "type": "TEXT_MESSAGE_CONTENT",
    "messageId": "msg_research_001",
    "delta": "Current stock price for Apple Inc. is $185.23...",
    "timestamp": "2025-07-01T10:01:15Z"
}
```

3. **TOOL_CALL_START Event**
```python
{
    "type": "TOOL_CALL_START",
    "toolCallId": "search_001",
    "toolCallName": "search_financial_data",
    "parentMessageId": "msg_research_001",
    "timestamp": "2025-07-01T10:00:30Z"
}
```

4. **STATE_DELTA Event**
```python
{
    "type": "STATE_DELTA",
    "threadId": "financial_analysis_thread",
    "delta": [
        {"op": "replace", "path": "/progress_percentage", "value": 25.0},
        {"op": "replace", "path": "/analysis_phase", "value": "evaluation"}
    ],
    "timestamp": "2025-07-01T10:02:00Z"
}
```

### 4.2 Message Handling Architecture

**Message Flow Design:**
```
Frontend Request → AG-UI Agent → MCP Agent Wrapper → MCP Tools → Results
                     ↓
                AG-UI Events ← Event Stream ← Processing Results
```

**Message Types:**
- **User Query**: "Analyze Apple Inc. stock performance"
- **System Messages**: Progress updates, status changes
- **Tool Results**: Financial data, analysis results
- **Error Messages**: Validation errors, API failures

### 4.3 Tool Integration Specifications

**Tool Definition Mapping:**
```python
MCP_TO_AGUI_TOOL_MAPPING = {
    "g-search": "search_financial_data",
    "fetch": "fetch_financial_document",
    "filesystem": "save_analysis_report"
}
```

**Tool Execution Flow:**
1. Frontend requests financial analysis
2. AG-UI agent emits TOOL_CALL_START
3. MCP tool executes with progress streaming
4. Results streamed via TOOL_CALL_ARGS
5. Completion signaled with TOOL_CALL_END

### 4.4 Error Handling and Lifecycle Management

**Error Event Structure:**
```python
{
    "type": "RUN_ERROR",
    "threadId": "financial_analysis_thread",
    "runId": "analysis_run_001",
    "error": {
        "code": "SEARCH_API_ERROR",
        "message": "Google Search API rate limit exceeded",
        "details": {"retry_after": 60}
    },
    "timestamp": "2025-07-01T10:05:00Z"
}
```

**Lifecycle Management:**
- **Graceful Degradation**: Fallback to cached data when APIs fail
- **Retry Logic**: Automatic retry with exponential backoff
- **Progress Tracking**: Real-time progress updates via STATE_DELTA
- **Resource Cleanup**: Proper cleanup of MCP connections and file handles

## 5. Testing Strategy

### 5.1 Unit Testing Approach

**Test Categories:**
1. **AG-UI Event Generation Tests**
2. **MCP-to-AG-UI Bridge Tests**
3. **State Management Tests**
4. **Tool Integration Tests**
5. **Error Handling Tests**

**Sample Test Structure:**
```python
# tests/test_ag_ui_integration.py
class TestAGUIIntegration:

    def test_research_agent_event_stream(self):
        """Test research agent generates correct AG-UI events"""
        # Setup mock MCP agent
        # Execute research workflow
        # Verify event sequence and content

    def test_state_management_updates(self):
        """Test state updates generate correct STATE_DELTA events"""
        # Initialize state manager
        # Apply state changes
        # Verify JSON Patch generation

    def test_tool_call_streaming(self):
        """Test tool calls generate proper streaming events"""
        # Mock financial data search
        # Verify TOOL_CALL_* event sequence
        # Validate tool result streaming
```

### 5.2 Integration Testing Strategy

**Integration Test Scenarios:**
1. **End-to-End Financial Analysis**: Complete workflow from user query to report
2. **Real-time Streaming**: Verify events stream correctly to frontend
3. **Error Recovery**: Test graceful handling of API failures
4. **State Synchronization**: Verify frontend state stays synchronized
5. **Performance Testing**: Measure streaming latency and throughput

**Test Environment Setup:**
```python
# tests/integration/test_full_workflow.py
class TestFullWorkflow:

    async def test_complete_financial_analysis(self):
        """Test complete financial analysis workflow with AG-UI events"""
        # Setup AG-UI agent with mock MCP backend
        # Execute full analysis for test company
        # Verify all events are generated correctly
        # Validate final report generation
        # Check state consistency throughout workflow
```

### 5.3 Validation Criteria

**Success Criteria:**
- ✅ All AG-UI events generated correctly
- ✅ Real-time streaming maintains <100ms latency
- ✅ State synchronization accuracy >99%
- ✅ Error recovery within 5 seconds
- ✅ Backward compatibility with existing MCP workflow
- ✅ Tool integration maintains existing functionality
- ✅ Memory usage increase <20% over baseline

**Performance Benchmarks:**
- Event generation: <10ms per event
- State update propagation: <50ms
- Tool call streaming: <200ms end-to-end
- Complete analysis workflow: <60 seconds

## 6. Implementation Timeline

**Phase 1 (Week 1-2): Foundation**
- Install AG-UI dependencies
- Create base AG-UI agent classes
- Implement event streaming infrastructure

**Phase 2 (Week 3-4): Agent Integration**
- Wrap existing agents with AG-UI interfaces
- Implement streaming for research and analysis phases
- Add state management capabilities

**Phase 3 (Week 5-6): Tool Integration**
- Map MCP tools to AG-UI tool definitions
- Implement tool call streaming
- Add progress tracking and error handling

**Phase 4 (Week 7-8): Testing & Optimization**
- Comprehensive testing suite
- Performance optimization
- Documentation and deployment preparation

## 7. Risk Mitigation

**Technical Risks:**
- **Event Streaming Performance**: Implement efficient buffering and batching
- **State Synchronization Complexity**: Use proven JSON Patch libraries
- **MCP Compatibility**: Maintain wrapper pattern for isolation

**Operational Risks:**
- **API Rate Limits**: Implement intelligent caching and retry logic
- **Memory Usage**: Monitor and optimize event stream memory footprint
- **Backward Compatibility**: Extensive regression testing

## 8. Success Metrics

**Technical Metrics:**
- Event streaming latency: <100ms average
- State synchronization accuracy: >99%
- Error recovery time: <5 seconds
- Memory overhead: <20% increase

**User Experience Metrics:**
- Real-time progress visibility
- Responsive frontend interaction
- Seamless error handling
- Consistent analysis quality

This comprehensive integration plan provides a roadmap for successfully implementing AG-UI protocol support while maintaining the robust financial analysis capabilities of the existing MCP system.
```
