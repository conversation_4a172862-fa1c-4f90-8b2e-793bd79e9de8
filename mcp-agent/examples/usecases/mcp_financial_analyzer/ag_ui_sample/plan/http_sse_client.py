"""
Standard HTTP Server-Sent Events (SSE) Client for MCP Financial Analyzer
======================================================================

HTTP SSE client implementation following AG-UI HttpAgent patterns for testing
connectivity to the MCP Financial Analyzer service.

This client provides:
- HTTP POST requests with RunAgentInput schema
- SSE stream parsing with BaseEvent validation
- Comprehensive error handling and logging
- Real-time event processing without buffering
- Integration testing compatibility

Usage:
    python http_sse_client.py --url http://localhost:8080/analyze --company "Apple Inc."

Example:
    client = FinancialAnalyzerSSEClient("http://localhost:8080/analyze")
    
    async for event in client.analyze_company("Apple Inc."):
        print(f"Received event: {event.type}")
        if event.type == "TEXT_MESSAGE_CONTENT":
            print(f"Content: {event.delta}")
"""

import asyncio
import argparse
import json
import logging
import uuid
from datetime import datetime
from typing import Dict, Any, Optional, AsyncGenerator, List
import aiohttp
from pydantic import ValidationError

# Import AG-UI core components
from ag_ui.core.types import RunAgentInput, Message, Tool, Context
from ag_ui.core.events import BaseEvent, EventType

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class SSEParseError(Exception):
    """Exception raised when SSE parsing fails."""
    pass


class EventValidationError(Exception):
    """Exception raised when event validation fails."""
    pass


class FinancialAnalyzerSSEClient:
    """
    Standard HTTP SSE client for MCP Financial Analyzer service.
    
    Follows AG-UI HttpAgent patterns for HTTP requests and SSE stream processing.
    Provides comprehensive error handling and real-time event streaming.
    """
    
    def __init__(
        self,
        url: str,
        headers: Optional[Dict[str, str]] = None,
        timeout: float = 30.0,
        validate_events: bool = True
    ):
        """
        Initialize the SSE client.
        
        Args:
            url: HTTP endpoint URL for the MCP Financial Analyzer service
            headers: Optional additional HTTP headers
            timeout: Request timeout in seconds
            validate_events: Whether to validate received events against BaseEvent schema
        """
        self.url = url
        self.timeout = aiohttp.ClientTimeout(total=timeout)
        self.validate_events = validate_events
        
        # Default headers following AG-UI HttpAgent pattern
        self.headers = {
            "Content-Type": "application/json",
            "Accept": "text/event-stream",
            "Cache-Control": "no-cache",
        }
        
        if headers:
            self.headers.update(headers)
        
        logger.info(f"Initialized FinancialAnalyzerSSEClient for {url}")
    
    def create_run_agent_input(
        self,
        company_name: str,
        thread_id: Optional[str] = None,
        run_id: Optional[str] = None,
        additional_context: Optional[List[Dict[str, str]]] = None
    ) -> RunAgentInput:
        """
        Create RunAgentInput payload for financial analysis request.
        
        Args:
            company_name: Name of the company to analyze
            thread_id: Optional thread ID (generated if not provided)
            run_id: Optional run ID (generated if not provided)
            additional_context: Optional additional context items
            
        Returns:
            RunAgentInput object conforming to AG-UI schema
        """
        if not thread_id:
            thread_id = f"thread_{uuid.uuid4()}"
        
        if not run_id:
            run_id = f"run_{uuid.uuid4()}"
        
        # Create context with company information
        context = [
            Context(
                description="company_name",
                value=company_name
            ),
            Context(
                description="analysis_type",
                value="comprehensive_financial_analysis"
            )
        ]
        
        # Add additional context if provided
        if additional_context:
            for ctx in additional_context:
                context.append(Context(
                    description=ctx.get("description", "additional_context"),
                    value=ctx.get("value", "")
                ))
        
        # Create user message requesting analysis
        messages = [
            Message(
                id=f"msg_{uuid.uuid4()}",
                role="user",
                content=f"Please perform a comprehensive financial analysis for {company_name}. "
                       f"Include current stock performance, recent earnings data, market position, "
                       f"and investment recommendations."
            )
        ]
        
        # Define available tools (empty for this client)
        tools: List[Tool] = []
        
        return RunAgentInput(
            thread_id=thread_id,
            run_id=run_id,
            state={},
            messages=messages,
            tools=tools,
            context=context,
            forwarded_props={}
        )
    
    async def parse_sse_stream(
        self,
        response: aiohttp.ClientResponse
    ) -> AsyncGenerator[BaseEvent, None]:
        """
        Parse Server-Sent Events stream into BaseEvent objects.
        
        Follows SSE standard:
        - Events separated by double newlines
        - Only 'data:' prefixed lines are processed
        - Multi-line data events are supported
        
        Args:
            response: aiohttp ClientResponse with SSE stream
            
        Yields:
            BaseEvent objects parsed from SSE stream
            
        Raises:
            SSEParseError: When SSE parsing fails
            EventValidationError: When event validation fails
        """
        buffer = ""
        
        try:
            async for chunk in response.content.iter_chunked(1024):
                # Decode chunk and add to buffer
                text = chunk.decode('utf-8')
                buffer += text
                
                # Process complete events (separated by double newlines)
                while '\n\n' in buffer:
                    event_data, buffer = buffer.split('\n\n', 1)
                    
                    if event_data.strip():
                        try:
                            # Parse SSE event
                            event_json = self._parse_sse_event(event_data)
                            if event_json:
                                # Validate and yield event
                                if self.validate_events:
                                    try:
                                        # Parse as BaseEvent for validation
                                        event = BaseEvent.model_validate(event_json)
                                        yield event
                                    except ValidationError as e:
                                        logger.warning(f"Event validation failed: {e}")
                                        logger.debug(f"Invalid event data: {event_json}")
                                        if self.validate_events:
                                            raise EventValidationError(f"Event validation failed: {e}")
                                else:
                                    # Create minimal BaseEvent without strict validation
                                    event = BaseEvent(
                                        type=event_json.get('type', 'UNKNOWN'),
                                        timestamp=event_json.get('timestamp'),
                                        raw_event=event_json
                                    )
                                    yield event
                        except Exception as e:
                            logger.error(f"Failed to parse SSE event: {e}")
                            logger.debug(f"Event data: {event_data}")
                            raise SSEParseError(f"Failed to parse SSE event: {e}")
        
        except Exception as e:
            logger.error(f"Error reading SSE stream: {e}")
            raise SSEParseError(f"Error reading SSE stream: {e}")
    
    def _parse_sse_event(self, event_data: str) -> Optional[Dict[str, Any]]:
        """
        Parse individual SSE event data.
        
        Args:
            event_data: Raw SSE event data
            
        Returns:
            Parsed JSON data or None if no data found
        """
        data_lines = []
        
        for line in event_data.split('\n'):
            line = line.strip()
            if line.startswith('data: '):
                data_lines.append(line[6:])  # Remove 'data: ' prefix
            elif line.startswith('data:'):
                data_lines.append(line[5:])  # Remove 'data:' prefix
        
        if data_lines:
            # Join multi-line data
            json_data = '\n'.join(data_lines)
            try:
                return json.loads(json_data)
            except json.JSONDecodeError as e:
                logger.warning(f"Failed to parse JSON from SSE data: {e}")
                logger.debug(f"JSON data: {json_data}")
                return None
        
        return None
    
    async def analyze_company(
        self,
        company_name: str,
        thread_id: Optional[str] = None,
        run_id: Optional[str] = None,
        additional_context: Optional[List[Dict[str, str]]] = None
    ) -> AsyncGenerator[BaseEvent, None]:
        """
        Perform financial analysis for a company and stream results.
        
        Args:
            company_name: Name of the company to analyze
            thread_id: Optional thread ID
            run_id: Optional run ID
            additional_context: Optional additional context
            
        Yields:
            BaseEvent objects from the analysis stream
            
        Raises:
            aiohttp.ClientError: For HTTP connection errors
            SSEParseError: For SSE parsing errors
            EventValidationError: For event validation errors
        """
        # Create request payload
        input_data = self.create_run_agent_input(
            company_name=company_name,
            thread_id=thread_id,
            run_id=run_id,
            additional_context=additional_context
        )
        
        logger.info(f"Starting analysis for {company_name}")
        logger.info(f"Thread ID: {input_data.thread_id}, Run ID: {input_data.run_id}")
        
        try:
            async with aiohttp.ClientSession(timeout=self.timeout) as session:
                # Send POST request
                async with session.post(
                    self.url,
                    headers=self.headers,
                    json=input_data.model_dump(by_alias=True, exclude_none=True)
                ) as response:
                    
                    # Check response status
                    if response.status != 200:
                        error_text = await response.text()
                        raise aiohttp.ClientResponseError(
                            request_info=response.request_info,
                            history=response.history,
                            status=response.status,
                            message=f"HTTP {response.status}: {error_text}"
                        )
                    
                    # Verify content type
                    content_type = response.headers.get('content-type', '')
                    if 'text/event-stream' not in content_type:
                        logger.warning(f"Unexpected content type: {content_type}")
                    
                    logger.info("Connected to SSE stream, processing events...")
                    
                    # Parse and yield events
                    async for event in self.parse_sse_stream(response):
                        logger.debug(f"Received event: {event.type}")
                        yield event
        
        except aiohttp.ClientError as e:
            logger.error(f"HTTP client error: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error during analysis: {e}")
            raise


async def test_connectivity(url: str, company_name: str = "Apple Inc.") -> bool:
    """
    Test connectivity to the MCP Financial Analyzer service.

    Args:
        url: Service endpoint URL
        company_name: Company name for test analysis

    Returns:
        True if connectivity test passes, False otherwise
    """
    logger.info(f"Testing connectivity to {url}")

    try:
        client = FinancialAnalyzerSSEClient(url, timeout=10.0)

        event_count = 0
        start_time = datetime.now()

        async for event in client.analyze_company(company_name):
            event_count += 1
            logger.info(f"Received event {event_count}: {event.type}")

            if event.type == EventType.TEXT_MESSAGE_CONTENT:
                logger.info(f"Content preview: {event.delta[:100]}...")

            # Stop after receiving a few events for connectivity test
            if event_count >= 5:
                break

        duration = (datetime.now() - start_time).total_seconds()
        logger.info(f"Connectivity test passed: {event_count} events in {duration:.2f}s")
        return True

    except Exception as e:
        logger.error(f"Connectivity test failed: {e}")
        return False


async def run_full_analysis(url: str, company_name: str) -> Dict[str, Any]:
    """
    Run a complete financial analysis and collect all events.

    Args:
        url: Service endpoint URL
        company_name: Company name to analyze

    Returns:
        Dictionary containing analysis results and metadata
    """
    logger.info(f"Running full analysis for {company_name}")

    client = FinancialAnalyzerSSEClient(url)

    events = []
    text_content = []
    start_time = datetime.now()

    try:
        async for event in client.analyze_company(company_name):
            events.append({
                "type": event.type,
                "timestamp": event.timestamp,
                "data": event.model_dump(exclude_none=True)
            })

            # Collect text content
            if event.type == EventType.TEXT_MESSAGE_CONTENT:
                text_content.append(event.delta)

            # Log progress
            if event.type == EventType.RUN_STARTED:
                logger.info("Analysis started")
            elif event.type == EventType.RUN_FINISHED:
                logger.info("Analysis completed")
                break
            elif event.type == EventType.RUN_ERROR:
                logger.error("Analysis failed")
                break

        duration = (datetime.now() - start_time).total_seconds()

        return {
            "status": "success",
            "company_name": company_name,
            "duration_seconds": duration,
            "event_count": len(events),
            "events": events,
            "text_content": "".join(text_content),
            "completed_at": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Analysis failed: {e}")
        return {
            "status": "error",
            "company_name": company_name,
            "error": str(e),
            "event_count": len(events),
            "events": events,
            "failed_at": datetime.now().isoformat()
        }


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="HTTP SSE Client for MCP Financial Analyzer"
    )

    parser.add_argument(
        "--url",
        type=str,
        default="http://localhost:8080/analyze",
        help="MCP Financial Analyzer service URL (default: http://localhost:8080/analyze)"
    )

    parser.add_argument(
        "--company",
        type=str,
        default="Apple Inc.",
        help="Company name to analyze (default: Apple Inc.)"
    )

    parser.add_argument(
        "--test-only",
        action="store_true",
        help="Run connectivity test only"
    )

    parser.add_argument(
        "--output",
        type=str,
        help="Output file for analysis results (JSON format)"
    )

    parser.add_argument(
        "--log-level",
        type=str,
        default="info",
        choices=["debug", "info", "warning", "error"],
        help="Log level (default: info)"
    )

    return parser.parse_args()


async def main():
    """Main entry point for the SSE client."""
    args = parse_arguments()

    # Set log level
    logging.getLogger().setLevel(getattr(logging, args.log_level.upper()))

    logger.info(f"MCP Financial Analyzer SSE Client")
    logger.info(f"Target URL: {args.url}")
    logger.info(f"Company: {args.company}")

    if args.test_only:
        # Run connectivity test only
        success = await test_connectivity(args.url, args.company)
        if success:
            logger.info("✅ Connectivity test passed")
            return 0
        else:
            logger.error("❌ Connectivity test failed")
            return 1
    else:
        # Run full analysis
        results = await run_full_analysis(args.url, args.company)

        if results["status"] == "success":
            logger.info(f"✅ Analysis completed successfully in {results['duration_seconds']:.2f}s")
            logger.info(f"📊 Received {results['event_count']} events")

            if results["text_content"]:
                logger.info("📝 Analysis content preview:")
                preview = results["text_content"][:500]
                logger.info(preview + ("..." if len(results["text_content"]) > 500 else ""))
        else:
            logger.error(f"❌ Analysis failed: {results['error']}")

        # Save results if output file specified
        if args.output:
            import json
            with open(args.output, 'w') as f:
                json.dump(results, f, indent=2)
            logger.info(f"Results saved to {args.output}")

        return 0 if results["status"] == "success" else 1


if __name__ == "__main__":
    import sys
    sys.exit(asyncio.run(main()))
