{"document_info": {"title": "Agent User Interaction Protocol (AG-UI) Documentation", "source_url": "https://docs.ag-ui.com/llms-full.txt", "indexed_date": "2025-07-01", "total_sections": 15, "description": "Comprehensive documentation for the AG-UI protocol, SDKs, and implementation guides"}, "table_of_contents": {"1": {"title": "Agents", "source": "https://docs.ag-ui.com/concepts/agents", "description": "Core components that process requests and generate responses", "subsections": ["What is an Agent?", "Agent Architecture", "Agent Types", "Implementing Agents", "Agent Capabilities", "Using Agents", "Agent Configuration", "Agent State Management"]}, "2": {"title": "Core Architecture", "source": "https://docs.ag-ui.com/concepts/architecture", "description": "Event-driven architecture connecting front-end applications to AI agents", "subsections": ["Overview", "Core Components", "Protocol Layer", "Standard HTTP Client", "Message Types", "Running Agents", "State Management", "Tools and Handoff", "Events"]}, "3": {"title": "Events", "source": "https://docs.ag-ui.com/concepts/events", "description": "Streaming event-based communication system", "subsections": ["Event Types Overview", "Base Event Properties", "Lifecycle Events", "Text Message Events", "Tool Call Events", "State Management Events"]}, "4": {"title": "JavaScript SDK - Core Types", "source": "https://docs.ag-ui.com/sdk/js/core/types", "description": "Core data structures for JavaScript SDK", "subsections": ["RunAgentInput", "Message Types", "Context", "Tool", "State"]}, "5": {"title": "JavaScript SDK - Encoder", "source": "https://docs.ag-ui.com/sdk/js/encoder", "description": "Event encoding utilities for JavaScript"}, "6": {"title": "JavaScript SDK - Proto", "source": "https://docs.ag-ui.com/sdk/js/proto", "description": "Protocol buffer definitions for JavaScript"}, "7": {"title": "Python SDK - Events", "source": "https://docs.ag-ui.com/sdk/python/core/events", "description": "Event system documentation for Python SDK", "subsections": ["EventType Enum", "BaseEvent", "Lifecycle Events", "Text Message Events", "Tool Call Events", "State Management Events", "Special Events", "Event Discrimination"]}, "8": {"title": "Python SDK - Overview", "source": "https://docs.ag-ui.com/sdk/python/core/overview", "description": "Core concepts in the Python SDK", "subsections": ["Types", "Events"]}, "9": {"title": "Python SDK - Types", "source": "https://docs.ag-ui.com/sdk/python/core/types", "description": "Core data structures for Python SDK", "subsections": ["RunAgentInput", "Message Types", "Context", "Tool", "State"]}, "10": {"title": "Python SDK - Encoder", "source": "https://docs.ag-ui.com/sdk/python/encoder/overview", "description": "Event encoding for Python SDK", "subsections": ["EventEncoder", "Usage", "Methods", "Example", "Implementation Details"]}, "11": {"title": "Cursor Development Tutorial", "source": "https://docs.ag-ui.com/tutorials/cursor", "description": "Guide for using Cursor IDE with AG-UI", "subsections": ["Adding Documentation to Cursor", "Using the Documentation", "Best Practices"]}, "12": {"title": "Debugging Guide", "source": "https://docs.ag-ui.com/tutorials/debugging", "description": "Comprehensive debugging guide for AG-UI integrations", "subsections": ["The AG-UI Dojo", "What is the Dojo?", "Using the Dojo as Implementation Checklist", "Using the Dojo as Learning Resource", "Common Debugging Patterns", "Getting Started with the Dojo"]}}, "key_concepts": {"agents": {"definition": "Core components that process requests and generate responses through event-driven streaming interface", "types": ["AbstractAgent", "HttpAgent", "Custom Agents"], "capabilities": ["Interactive Communication", "Tool Usage", "State Management", "Multi-Agent Collaboration", "Human-in-the-Loop Workflows", "Conversational Memory", "Metadata and Instrumentation"], "related_sections": ["1", "2"]}, "events": {"definition": "Fundamental units of communication between agents and frontends enabling real-time structured interaction", "categories": ["Lifecycle Events", "Text Message Events", "Tool Call Events", "State Management Events", "Special Events"], "streaming_pattern": "Events follow streaming patterns with start, content chunks, and end events", "related_sections": ["3", "7"]}, "protocol": {"definition": "Standardized communication protocol between front-end applications and AI agents", "architecture": "Client-server event-driven architecture", "transports": ["HTTP SSE (Server-Sent Events)", "HTTP binary protocol"], "related_sections": ["2"]}, "tools": {"definition": "Functions that agents can use to interact with external systems", "flow": "Defined by frontend, passed to agent, invoked through event sequence", "events": ["TOOL_CALL_START", "TOOL_CALL_ARGS", "TOOL_CALL_END"], "related_sections": ["1", "3", "7"]}, "state_management": {"definition": "Structured data that persists across agent interactions", "methods": ["STATE_SNAPSHOT", "STATE_DELTA"], "format": "JSON Patch format (RFC 6902) for deltas", "related_sections": ["1", "2", "3", "7"]}}, "keyword_index": {"AbstractAgent": {"type": "class", "description": "Base class that all agents extend, handles core event processing, state management, and message history", "sections": ["1"], "related_terms": ["HttpAgent", "CustomAgent", "agent", "implementation"]}, "HttpAgent": {"type": "class", "description": "Concrete implementation that connects to remote AI services via HTTP", "sections": ["1", "2"], "related_terms": ["AbstractAgent", "HTTP", "endpoint", "API"]}, "RunAgentInput": {"type": "interface", "description": "Input parameters for running an agent, body of POST request in HTTP API", "sections": ["4", "9"], "related_terms": ["threadId", "runId", "state", "messages", "tools", "context"]}, "EventType": {"type": "enum", "description": "Defines all possible event types in the system", "sections": ["3", "7"], "related_terms": ["events", "streaming", "communication"]}, "BaseEvent": {"type": "class", "description": "Common properties shared across all event types", "sections": ["3", "7"], "related_terms": ["timestamp", "rawEvent", "type"]}, "TEXT_MESSAGE_START": {"type": "event", "description": "Signals the start of a text message", "sections": ["3", "7"], "related_terms": ["messageId", "role", "assistant", "streaming"]}, "TEXT_MESSAGE_CONTENT": {"type": "event", "description": "Represents a chunk of content in streaming text message", "sections": ["3", "7"], "related_terms": ["delta", "messageId", "streaming", "chunk"]}, "TEXT_MESSAGE_END": {"type": "event", "description": "Signals the end of a text message", "sections": ["3", "7"], "related_terms": ["messageId", "completion"]}, "TOOL_CALL_START": {"type": "event", "description": "Signals the start of a tool call", "sections": ["3", "7"], "related_terms": ["toolCallId", "toolCallName", "parentMessageId"]}, "TOOL_CALL_ARGS": {"type": "event", "description": "Represents chunk of argument data for tool call", "sections": ["3", "7"], "related_terms": ["toolCallId", "delta", "arguments", "JSON"]}, "TOOL_CALL_END": {"type": "event", "description": "Signals the end of a tool call", "sections": ["3", "7"], "related_terms": ["toolCallId", "completion"]}, "STATE_SNAPSHOT": {"type": "event", "description": "Provides complete snapshot of agent's state", "sections": ["3", "7"], "related_terms": ["snapshot", "state", "synchronization"]}, "STATE_DELTA": {"type": "event", "description": "Provides partial update to agent's state using JSON Patch", "sections": ["3", "7"], "related_terms": ["delta", "JSON Patch", "RFC 6902", "incremental"]}, "RUN_STARTED": {"type": "event", "description": "Signals the start of an agent run", "sections": ["3", "7"], "related_terms": ["threadId", "runId", "lifecycle"]}, "RUN_FINISHED": {"type": "event", "description": "Signals successful completion of agent run", "sections": ["3", "7"], "related_terms": ["threadId", "runId", "completion", "success"]}, "RUN_ERROR": {"type": "event", "description": "Signals error during agent run", "sections": ["3", "7"], "related_terms": ["message", "code", "error", "failure"]}}}