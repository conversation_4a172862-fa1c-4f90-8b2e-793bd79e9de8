# MCP Financial Analyzer HTTP Server and SSE Client

This directory contains the HTTP Server-Sent Events (SSE) implementation for the MCP Financial Analyzer, following AG-UI HttpAgent patterns for real-time financial analysis streaming.

## Overview

The implementation consists of three main components:

1. **HTTP Server** (`http_server.py`) - FastAPI-based server exposing MCP Financial Analyzer via HTTP with SSE streaming
2. **SSE Client** (`http_sse_client.py`) - Standard HTTP client following AG-UI patterns for testing connectivity
3. **Integration Tests** (`test_integration.py`) - Comprehensive test suite validating server-client integration

## Architecture

```
┌─────────────────┐    HTTP POST     ┌──────────────────┐    AG-UI Events    ┌─────────────────┐
│   SSE Client    │ ──────────────► │   HTTP Server    │ ─────────────────► │ MCP Orchestrator│
│                 │                 │                  │                    │                 │
│ - RunAgentInput │                 │ - FastAPI        │                    │ - AG-UI Workflow│
│ - SSE Parsing   │ ◄────────────── │ - Event Encoding │ ◄───────────────── │ - Tool Bridge   │
│ - Event Validation                │ - CORS Support   │    BaseEvent       │ - State Mgmt    │
└─────────────────┘    SSE Stream   └──────────────────┘                    └─────────────────┘
```

## Quick Start

### 1. Install Dependencies

```bash
# Navigate to MCP Financial Analyzer directory
cd /merge/mcp-agent/examples/usecases/mcp_financial_analyzer

# Install Python dependencies
pip install fastapi uvicorn aiohttp pytest pytest-asyncio

# Install AG-UI Python SDK (if not already installed)
pip install -e /merge/project/ag-ui/python-sdk
```

### 2. Start the HTTP Server

```bash
# Start server with default settings (localhost:8080)
python http_server.py

# Start server with custom host/port
python http_server.py --host 0.0.0.0 --port 8000

# Start with auto-reload for development
python http_server.py --reload --log-level debug
```

### 3. Test with SSE Client

```bash
# Navigate to the plan directory
cd ag_ui/plan

# Run connectivity test
python http_sse_client.py --test-only

# Analyze a company
python http_sse_client.py --company "Apple Inc."

# Save results to file
python http_sse_client.py --company "Microsoft" --output results.json
```

### 4. Run Integration Tests

```bash
# Run all integration tests
python test_integration.py

# Run with pytest
pytest test_integration.py -v

# Wait for server and run tests
python test_integration.py --wait-for-server --output test_results.json
```

## HTTP Server Details

### Endpoints

#### `GET /health`
Health check endpoint returning server status.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00Z",
  "service": "MCP Financial Analyzer HTTP Server"
}
```

#### `POST /analyze`
Main analysis endpoint accepting RunAgentInput and returning SSE stream.

**Request:**
```json
{
  "threadId": "thread_123",
  "runId": "run_456",
  "state": {},
  "messages": [
    {
      "id": "msg_1",
      "role": "user",
      "content": "Analyze Apple Inc."
    }
  ],
  "tools": [],
  "context": [
    {
      "description": "company_name",
      "value": "Apple Inc."
    }
  ],
  "forwardedProps": {}
}
```

**Response:** Server-Sent Events stream with BaseEvent objects:
```
data: {"type": "RUN_STARTED", "timestamp": 1705312200000}

data: {"type": "TEXT_MESSAGE_START", "message_id": "msg_123", "role": "assistant"}

data: {"type": "TEXT_MESSAGE_CONTENT", "message_id": "msg_123", "delta": "Starting analysis..."}

data: {"type": "RUN_FINISHED", "timestamp": 1705312260000}
```

### Configuration

The server supports the following command-line arguments:

- `--host`: Host to bind to (default: localhost)
- `--port`: Port to bind to (default: 8080)
- `--reload`: Enable auto-reload for development
- `--log-level`: Log level (debug, info, warning, error)

### Environment Variables

- `MCP_CONFIG_PATH`: Path to MCP configuration file
- `AGUI_LOG_LEVEL`: AG-UI logging level

## SSE Client Details

### Basic Usage

```python
from http_sse_client import FinancialAnalyzerSSEClient

# Create client
client = FinancialAnalyzerSSEClient("http://localhost:8080/analyze")

# Analyze company
async for event in client.analyze_company("Apple Inc."):
    print(f"Event: {event.type}")
    if event.type == "TEXT_MESSAGE_CONTENT":
        print(f"Content: {event.delta}")
```

### Advanced Configuration

```python
# Custom headers and timeout
client = FinancialAnalyzerSSEClient(
    url="http://localhost:8080/analyze",
    headers={"Authorization": "Bearer token"},
    timeout=60.0,
    validate_events=True
)

# Custom context
additional_context = [
    {"description": "analysis_depth", "value": "comprehensive"},
    {"description": "include_competitors", "value": "true"}
]

async for event in client.analyze_company(
    company_name="Apple Inc.",
    additional_context=additional_context
):
    # Process events
    pass
```

### Error Handling

The client provides comprehensive error handling:

- `SSEParseError`: SSE stream parsing failures
- `EventValidationError`: Event schema validation failures
- `aiohttp.ClientError`: HTTP connection errors

## Integration Testing

### Test Categories

1. **Server Health** - Validates server availability and health endpoint
2. **Basic Connectivity** - Tests connection to analysis endpoint
3. **Schema Validation** - Validates RunAgentInput and BaseEvent schemas
4. **SSE Stream Parsing** - Tests real-time event stream processing
5. **Error Handling** - Validates error scenarios and recovery
6. **End-to-End Workflow** - Complete analysis workflow validation

### Running Tests

```bash
# Run all tests
python test_integration.py

# Run specific test with pytest
pytest test_integration.py::test_schema_validation -v

# Run with custom server URL
python test_integration.py --server-url http://production:8080/analyze

# Generate test report
python test_integration.py --output integration_report.json
```

### Test Results

Test results include:
- Individual test outcomes (PASS/FAIL/ERROR)
- Performance metrics (duration, events per second)
- Error details and stack traces
- Overall success rate and summary

## Deployment

### Production Deployment

1. **Server Configuration:**
```bash
# Production server with proper host binding
python http_server.py --host 0.0.0.0 --port 8080 --log-level info
```

2. **Reverse Proxy (nginx):**
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        
        # SSE specific settings
        proxy_buffering off;
        proxy_cache off;
        proxy_set_header Connection '';
        proxy_http_version 1.1;
        chunked_transfer_encoding off;
    }
}
```

3. **Docker Deployment:**
```dockerfile
FROM python:3.10-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8080

CMD ["python", "http_server.py", "--host", "0.0.0.0", "--port", "8080"]
```

### Monitoring

Monitor the following metrics:
- Request rate and response times
- SSE connection duration and event throughput
- Error rates and types
- Memory and CPU usage
- MCP server connectivity

### Security Considerations

- Implement authentication/authorization for production
- Use HTTPS for encrypted communication
- Configure CORS appropriately for your domain
- Rate limiting for API endpoints
- Input validation and sanitization

## Troubleshooting

### Common Issues

1. **Server won't start:**
   - Check port availability: `netstat -tlnp | grep 8080`
   - Verify MCP dependencies are installed
   - Check AG-UI SDK installation

2. **SSE connection fails:**
   - Verify server is running: `curl http://localhost:8080/health`
   - Check firewall settings
   - Validate request headers (Accept: text/event-stream)

3. **Event parsing errors:**
   - Enable debug logging: `--log-level debug`
   - Check event schema compliance
   - Verify JSON formatting in SSE stream

4. **Integration test failures:**
   - Ensure server is running before tests
   - Check network connectivity
   - Verify AG-UI dependencies

### Debug Mode

Enable comprehensive debugging:

```bash
# Server debug mode
python http_server.py --log-level debug --reload

# Client debug mode
python http_sse_client.py --log-level debug --company "Test"

# Test debug mode
python test_integration.py --log-level debug --wait-for-server
```

## Contributing

When contributing to this implementation:

1. Follow AG-UI HttpAgent patterns exactly
2. Maintain schema compliance with RunAgentInput/BaseEvent
3. Add comprehensive tests for new features
4. Update documentation for API changes
5. Ensure backward compatibility

## Support

For issues and questions:
- Check the troubleshooting section above
- Review integration test results for specific failures
- Enable debug logging for detailed error information
- Consult AG-UI documentation for schema details
