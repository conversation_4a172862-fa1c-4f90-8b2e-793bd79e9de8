# AG-UI Technical Specifications for MCP Financial Analyzer

## 1. Event Type Specifications

### 1.1 Lifecycle Events

**RUN_STARTED Event**
```json
{
  "type": "RUN_STARTED",
  "threadId": "financial_analysis_thread_001",
  "runId": "analysis_run_20250701_001",
  "timestamp": "2025-07-01T10:00:00.000Z",
  "metadata": {
    "agent_name": "financial_research_agent",
    "company_name": "Apple Inc.",
    "analysis_type": "comprehensive_financial_analysis",
    "estimated_duration_minutes": 5,
    "workflow_version": "1.0"
  }
}
```

**RUN_FINISHED Event**
```json
{
  "type": "RUN_FINISHED",
  "threadId": "financial_analysis_thread_001",
  "runId": "analysis_run_20250701_001",
  "timestamp": "2025-07-01T10:05:23.456Z",
  "metadata": {
    "agent_name": "financial_report_writer",
    "completion_status": "success",
    "final_progress": 100.0,
    "execution_time_seconds": 323.456,
    "output_files": ["./reports/apple_analysis_20250701.md"]
  }
}
```

**RUN_ERROR Event**
```json
{
  "type": "RUN_ERROR",
  "threadId": "financial_analysis_thread_001",
  "runId": "analysis_run_20250701_001",
  "timestamp": "2025-07-01T10:02:15.789Z",
  "error": {
    "code": "SEARCH_API_RATE_LIMIT",
    "message": "Google Search API rate limit exceeded",
    "details": {
      "retry_after_seconds": 60,
      "current_quota": 0,
      "quota_reset_time": "2025-07-01T11:00:00.000Z"
    },
    "recovery_strategy": "wait_and_retry",
    "agent_name": "financial_research_agent"
  }
}
```

### 1.2 Text Message Events

**TEXT_MESSAGE_START Event**
```json
{
  "type": "TEXT_MESSAGE_START",
  "messageId": "msg_research_findings_001",
  "role": "assistant",
  "timestamp": "2025-07-01T10:01:30.123Z",
  "metadata": {
    "message_category": "research_findings",
    "company_name": "Apple Inc.",
    "expected_length": "medium",
    "content_type": "financial_analysis"
  }
}
```

**TEXT_MESSAGE_CONTENT Event**
```json
{
  "type": "TEXT_MESSAGE_CONTENT",
  "messageId": "msg_research_findings_001",
  "delta": "📊 **Apple Inc. Stock Analysis**\n\nCurrent stock price: $185.23 (+2.1% today)\nMarket cap: $2.89T\n",
  "timestamp": "2025-07-01T10:01:30.234Z",
  "metadata": {
    "chunk_index": 1,
    "total_chunks_estimated": 15,
    "content_section": "stock_performance"
  }
}
```

**TEXT_MESSAGE_END Event**
```json
{
  "type": "TEXT_MESSAGE_END",
  "messageId": "msg_research_findings_001",
  "timestamp": "2025-07-01T10:01:45.567Z",
  "metadata": {
    "final_word_count": 342,
    "total_chunks_sent": 14,
    "message_duration_seconds": 15.444,
    "content_complete": true
  }
}
```

### 1.3 Tool Call Events

**TOOL_CALL_START Event**
```json
{
  "type": "TOOL_CALL_START",
  "toolCallId": "search_financial_data_001",
  "toolCallName": "search_financial_data",
  "parentMessageId": "msg_research_findings_001",
  "timestamp": "2025-07-01T10:00:45.123Z",
  "metadata": {
    "tool_category": "data_collection",
    "expected_duration_seconds": 3,
    "mcp_server": "g-search",
    "operation_type": "financial_search"
  }
}
```

**TOOL_CALL_ARGS Event**
```json
{
  "type": "TOOL_CALL_ARGS",
  "toolCallId": "search_financial_data_001",
  "delta": "{\"query\": \"Apple Inc stock price today current market data\", \"search_type\": \"financial\", \"time_filter\": \"recent\"}",
  "timestamp": "2025-07-01T10:00:45.234Z",
  "metadata": {
    "args_chunk_index": 1,
    "args_complete": true,
    "validation_status": "valid"
  }
}
```

**TOOL_CALL_END Event**
```json
{
  "type": "TOOL_CALL_END",
  "toolCallId": "search_financial_data_001",
  "timestamp": "2025-07-01T10:00:48.567Z",
  "result": {
    "status": "success",
    "data": {
      "stock_price": "$185.23",
      "price_change": "+$3.78 (+2.1%)",
      "volume": "52.3M",
      "market_cap": "$2.89T",
      "sources": [
        "https://finance.yahoo.com/quote/AAPL",
        "https://www.bloomberg.com/quote/AAPL:US"
      ]
    },
    "execution_time_ms": 3344,
    "data_freshness": "real_time"
  }
}
```

### 1.4 State Management Events

**STATE_SNAPSHOT Event**
```json
{
  "type": "STATE_SNAPSHOT",
  "threadId": "financial_analysis_thread_001",
  "timestamp": "2025-07-01T10:00:00.000Z",
  "state": {
    "company_name": "Apple Inc.",
    "analysis_phase": "research",
    "progress_percentage": 25.0,
    "current_operation": "financial_data_collection",
    "research_data": {
      "stock_price": "$185.23",
      "earnings_data": null,
      "news_items": []
    },
    "analysis_results": null,
    "error_state": null,
    "workflow_metadata": {
      "start_time": "2025-07-01T10:00:00.000Z",
      "estimated_completion": "2025-07-01T10:05:00.000Z",
      "agent_sequence": ["research", "analysis", "reporting"],
      "current_agent_index": 0
    }
  }
}
```

**STATE_DELTA Event**
```json
{
  "type": "STATE_DELTA",
  "threadId": "financial_analysis_thread_001",
  "timestamp": "2025-07-01T10:01:00.000Z",
  "delta": [
    {
      "op": "replace",
      "path": "/progress_percentage",
      "value": 45.0
    },
    {
      "op": "replace",
      "path": "/current_operation",
      "value": "earnings_data_analysis"
    },
    {
      "op": "add",
      "path": "/research_data/earnings_data",
      "value": {
        "quarter": "Q1 2025",
        "revenue": "$89.5B",
        "eps": "$1.52",
        "beat_estimates": true
      }
    }
  ]
}
```

## 2. Message Handling Architecture

### 2.1 Message Flow Design

```
User Query → AG-UI Agent → Event Stream → Frontend
    ↓              ↓            ↓
MCP Agent → MCP Tools → Results → AG-UI Events
```

### 2.2 Message Categories

**Financial Query Messages**
- Stock performance requests
- Earnings analysis requests  
- Market comparison requests
- Risk assessment requests

**System Status Messages**
- Progress updates
- Phase transitions
- Error notifications
- Completion confirmations

**Tool Result Messages**
- Search results
- Analysis outputs
- Report generation status
- File operation confirmations

### 2.3 Message Validation Schema

```python
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime

class AGUIEventBase(BaseModel):
    type: str = Field(..., description="AG-UI event type")
    timestamp: str = Field(..., description="ISO 8601 timestamp")
    threadId: Optional[str] = Field(None, description="Thread identifier")
    runId: Optional[str] = Field(None, description="Run identifier")

class TextMessageContentEvent(AGUIEventBase):
    messageId: str = Field(..., description="Message identifier")
    delta: str = Field(..., description="Content chunk")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")

class ToolCallStartEvent(AGUIEventBase):
    toolCallId: str = Field(..., description="Tool call identifier")
    toolCallName: str = Field(..., description="Name of the tool being called")
    parentMessageId: Optional[str] = Field(None, description="Parent message ID")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Tool metadata")

class StateDeltaEvent(AGUIEventBase):
    delta: List[Dict[str, Any]] = Field(..., description="JSON Patch operations")
```

## 3. Tool Integration Specifications

### 3.1 Financial Analysis Tool Definitions

```python
FINANCIAL_ANALYSIS_TOOLS = [
    {
        "name": "search_financial_data",
        "description": "Search for current financial data and market information",
        "parameters": {
            "type": "object",
            "properties": {
                "company_name": {
                    "type": "string",
                    "description": "Name of the company to research"
                },
                "query_type": {
                    "type": "string",
                    "enum": ["stock_price", "earnings", "news", "analyst_ratings", "financials"],
                    "description": "Type of financial data to search for"
                },
                "time_period": {
                    "type": "string",
                    "enum": ["today", "week", "month", "quarter", "year"],
                    "description": "Time period for the data"
                },
                "include_sources": {
                    "type": "boolean",
                    "default": true,
                    "description": "Whether to include source URLs"
                }
            },
            "required": ["company_name", "query_type"]
        }
    },
    {
        "name": "analyze_financial_metrics",
        "description": "Analyze financial metrics and generate insights",
        "parameters": {
            "type": "object",
            "properties": {
                "research_data": {
                    "type": "string",
                    "description": "Raw financial research data to analyze"
                },
                "analysis_type": {
                    "type": "string",
                    "enum": ["performance", "valuation", "risk", "comparison", "comprehensive"],
                    "description": "Type of analysis to perform"
                },
                "benchmark_companies": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Companies to use for comparison analysis"
                },
                "focus_metrics": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Specific metrics to focus on"
                }
            },
            "required": ["research_data", "analysis_type"]
        }
    },
    {
        "name": "generate_financial_report",
        "description": "Generate comprehensive financial analysis report",
        "parameters": {
            "type": "object",
            "properties": {
                "company_name": {
                    "type": "string",
                    "description": "Company name for the report"
                },
                "analysis_data": {
                    "type": "string",
                    "description": "Analyzed financial data"
                },
                "output_format": {
                    "type": "string",
                    "enum": ["markdown", "html", "pdf"],
                    "default": "markdown",
                    "description": "Output format for the report"
                },
                "output_path": {
                    "type": "string",
                    "description": "File path for report output"
                },
                "include_sections": {
                    "type": "array",
                    "items": {
                        "type": "string",
                        "enum": ["executive_summary", "stock_performance", "earnings_analysis", 
                                "market_position", "risk_factors", "recommendations", "appendix"]
                    },
                    "default": ["executive_summary", "stock_performance", "earnings_analysis", "recommendations"],
                    "description": "Sections to include in the report"
                }
            },
            "required": ["company_name", "analysis_data", "output_path"]
        }
    }
]
```

### 3.2 Tool Execution Mapping

```python
MCP_TO_AGUI_TOOL_MAPPING = {
    # MCP Server -> AG-UI Tool Name
    "g-search": "search_financial_data",
    "fetch": "fetch_financial_document",
    "filesystem": "generate_financial_report",
    
    # Tool Operation Mapping
    "search_operations": {
        "stock_price": "g-search",
        "earnings": "g-search", 
        "news": "g-search",
        "analyst_ratings": "g-search"
    },
    
    "analysis_operations": {
        "performance": "internal_analysis",
        "valuation": "internal_analysis",
        "risk": "internal_analysis"
    },
    
    "output_operations": {
        "markdown": "filesystem",
        "html": "filesystem", 
        "pdf": "filesystem"
    }
}
```

### 3.3 Tool Result Schemas

```python
class FinancialSearchResult(BaseModel):
    status: str = Field(..., description="Execution status")
    data: Dict[str, Any] = Field(..., description="Financial data results")
    sources: List[str] = Field(default_factory=list, description="Data source URLs")
    execution_time_ms: int = Field(..., description="Execution time in milliseconds")
    data_freshness: str = Field(..., description="Data freshness indicator")
    confidence_score: Optional[float] = Field(None, description="Data confidence score")

class FinancialAnalysisResult(BaseModel):
    status: str = Field(..., description="Analysis status")
    insights: Dict[str, Any] = Field(..., description="Analysis insights")
    metrics: Dict[str, float] = Field(..., description="Calculated metrics")
    recommendations: List[str] = Field(default_factory=list, description="Analysis recommendations")
    confidence_score: float = Field(..., description="Analysis confidence score")
    methodology: str = Field(..., description="Analysis methodology used")

class ReportGenerationResult(BaseModel):
    status: str = Field(..., description="Generation status")
    file_path: str = Field(..., description="Generated report file path")
    word_count: int = Field(..., description="Report word count")
    sections_included: List[str] = Field(..., description="Sections included in report")
    generation_time_seconds: float = Field(..., description="Generation time")
    file_size_bytes: int = Field(..., description="Generated file size")

## 4. Error Handling and Recovery

### 4.1 Error Classification

**API Errors**
- Rate limiting (Google Search API, OpenAI API)
- Authentication failures
- Service unavailability
- Quota exceeded

**Data Errors**
- Invalid company names
- Missing financial data
- Stale data warnings
- Data validation failures

**System Errors**
- Memory limitations
- File system errors
- Network connectivity issues
- Process timeouts

**Workflow Errors**
- Agent execution failures
- State synchronization errors
- Event streaming interruptions
- Tool integration failures

### 4.2 Error Event Specifications

```python
class ErrorEvent(BaseModel):
    type: str = Field(default="RUN_ERROR", description="Error event type")
    threadId: str = Field(..., description="Thread identifier")
    runId: str = Field(..., description="Run identifier")
    timestamp: str = Field(..., description="Error timestamp")
    error: ErrorDetails = Field(..., description="Error details")

class ErrorDetails(BaseModel):
    code: str = Field(..., description="Error code")
    message: str = Field(..., description="Human-readable error message")
    details: Dict[str, Any] = Field(default_factory=dict, description="Additional error details")
    recovery_strategy: str = Field(..., description="Suggested recovery strategy")
    agent_name: Optional[str] = Field(None, description="Agent that encountered the error")
    retry_count: int = Field(default=0, description="Number of retry attempts")
    max_retries: int = Field(default=3, description="Maximum retry attempts")
```

### 4.3 Recovery Strategies

**Automatic Recovery**
```python
RECOVERY_STRATEGIES = {
    "SEARCH_API_RATE_LIMIT": {
        "strategy": "exponential_backoff",
        "initial_delay_seconds": 60,
        "max_delay_seconds": 300,
        "max_retries": 3,
        "fallback": "use_cached_data"
    },

    "NETWORK_TIMEOUT": {
        "strategy": "immediate_retry",
        "max_retries": 2,
        "timeout_increase_factor": 1.5,
        "fallback": "partial_analysis"
    },

    "DATA_VALIDATION_ERROR": {
        "strategy": "data_cleaning",
        "cleaning_methods": ["remove_invalid", "interpolate_missing"],
        "fallback": "manual_review_required"
    },

    "AGENT_EXECUTION_ERROR": {
        "strategy": "agent_restart",
        "preserve_state": true,
        "max_restarts": 2,
        "fallback": "skip_agent_phase"
    }
}
```

### 4.4 Graceful Degradation

**Service Degradation Levels**
1. **Full Service**: All features operational
2. **Limited Service**: Core analysis with reduced data sources
3. **Basic Service**: Cached data analysis only
4. **Emergency Mode**: Error reporting and state preservation

**Degradation Implementation**
```python
class ServiceDegradationManager:
    def __init__(self):
        self.current_level = "full_service"
        self.degradation_triggers = {
            "api_failures": 3,
            "consecutive_errors": 5,
            "response_time_threshold_ms": 10000
        }

    def assess_service_level(self, error_history: List[Dict]) -> str:
        """Assess current service degradation level"""
        # Implementation logic for service assessment
        pass

    def apply_degradation(self, level: str, observer):
        """Apply service degradation and notify via events"""
        # Emit service degradation event
        # Adjust tool availability
        # Update user expectations
        pass
```

## 5. Performance Specifications

### 5.1 Latency Requirements

**Event Generation**
- Event creation: <10ms per event
- Event serialization: <5ms per event
- Event transmission: <20ms per event

**State Management**
- State update processing: <50ms
- JSON Patch generation: <25ms
- State synchronization: <100ms

**Tool Execution**
- Search operations: <3000ms average
- Analysis operations: <5000ms average
- Report generation: <10000ms average

**End-to-End Workflow**
- Complete financial analysis: <60 seconds
- Real-time progress updates: <100ms latency
- Error recovery: <5 seconds

### 5.2 Throughput Requirements

**Event Streaming**
- Events per second: 100+ sustained
- Concurrent analyses: 10+ simultaneous
- Message throughput: 1MB/s per analysis

**Resource Utilization**
- Memory overhead: <20% increase over baseline
- CPU utilization: <80% during peak analysis
- Network bandwidth: <10MB per complete analysis

### 5.3 Scalability Considerations

**Horizontal Scaling**
- Stateless agent design for load balancing
- Event stream partitioning by threadId
- Independent tool execution scaling

**Vertical Scaling**
- Memory-efficient event buffering
- Lazy loading of analysis data
- Streaming result processing

## 6. Security and Compliance

### 6.1 Data Security

**Financial Data Protection**
- Encryption in transit (TLS 1.3)
- Temporary data storage only
- Automatic data purging after analysis
- No persistent storage of sensitive data

**API Key Management**
- Secure credential storage
- API key rotation support
- Rate limiting compliance
- Usage monitoring and alerting

### 6.2 Privacy Compliance

**Data Minimization**
- Collect only necessary financial data
- Aggregate data when possible
- Remove personally identifiable information
- Implement data retention policies

**Audit Trail**
- Log all financial data access
- Track analysis request origins
- Monitor tool usage patterns
- Generate compliance reports

### 6.3 Access Control

**Authentication**
- User authentication for analysis requests
- API key validation for tool access
- Session management for long-running analyses

**Authorization**
- Role-based access to financial tools
- Company-specific data access controls
- Analysis result sharing permissions

## 7. Monitoring and Observability

### 7.1 Metrics Collection

**Performance Metrics**
```python
PERFORMANCE_METRICS = {
    "event_generation_latency_ms": "histogram",
    "tool_execution_duration_ms": "histogram",
    "analysis_completion_time_seconds": "histogram",
    "state_update_frequency": "counter",
    "error_rate_by_type": "counter",
    "concurrent_analyses": "gauge",
    "memory_usage_mb": "gauge",
    "api_quota_remaining": "gauge"
}
```

**Business Metrics**
```python
BUSINESS_METRICS = {
    "analyses_completed_total": "counter",
    "companies_analyzed_unique": "counter",
    "reports_generated_total": "counter",
    "user_satisfaction_score": "histogram",
    "analysis_accuracy_score": "histogram",
    "time_to_first_insight_seconds": "histogram"
}
```

### 7.2 Health Checks

**System Health Endpoints**
- `/health/live`: Basic liveness check
- `/health/ready`: Readiness for new analyses
- `/health/detailed`: Comprehensive system status

**Health Check Implementation**
```python
class HealthChecker:
    def check_mcp_connectivity(self) -> bool:
        """Check MCP server connectivity"""
        pass

    def check_api_quotas(self) -> Dict[str, Any]:
        """Check API quota status"""
        pass

    def check_agent_status(self) -> Dict[str, str]:
        """Check individual agent health"""
        pass

    def check_event_stream_health(self) -> bool:
        """Check event streaming functionality"""
        pass
```

### 7.3 Alerting

**Critical Alerts**
- Analysis failure rate >10%
- API quota exhaustion
- Event streaming interruption
- Agent unresponsiveness

**Warning Alerts**
- Analysis latency >30 seconds
- Error rate >5%
- Memory usage >80%
- API quota <20% remaining

**Alert Configuration**
```python
ALERT_RULES = {
    "analysis_failure_rate": {
        "threshold": 0.10,
        "window_minutes": 5,
        "severity": "critical",
        "notification_channels": ["slack", "email"]
    },

    "high_latency": {
        "threshold_seconds": 30,
        "window_minutes": 2,
        "severity": "warning",
        "notification_channels": ["slack"]
    }
}
```

This comprehensive technical specification provides the detailed requirements and implementation guidelines for successfully integrating AG-UI protocol support with the MCP Financial Analyzer while maintaining high performance, reliability, and security standards.
```
