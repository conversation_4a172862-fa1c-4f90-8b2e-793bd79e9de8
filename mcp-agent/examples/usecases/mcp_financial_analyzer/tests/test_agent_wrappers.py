"""
Unit tests for AG-UI agent wrappers.

Tests the AG-UI enabled versions of financial analysis agents including
research agent, analyst agent, and report writer with streaming capabilities.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from ag_ui_sample.agent_wrappers import AGUIResearchAgent, AGUIAnalystAgent, AGUIReportWriter
from ag_ui_sample.state_manager import AGUIStateManager
from ag_ui_sample.event_stream import FinancialAnalysisEventStream
from ag_ui_sample.tool_bridge import MCPToAGUIToolBridge


class TestAGUIResearchAgent:
    """Test AG-UI research agent functionality."""
    
    @pytest.fixture
    def setup_components(self):
        """Setup AG-UI components for testing."""
        state_manager = AGUIStateManager()
        event_stream = FinancialAnalysisEventStream()
        tool_bridge = MCPToAGUIToolBridge()
        return state_manager, event_stream, tool_bridge
    
    @pytest.fixture
    def research_agent(self, setup_components):
        """Create research agent for testing."""
        state_manager, event_stream, tool_bridge = setup_components
        return AGUIResearchAgent(
            company_name="AAPL",
            state_manager=state_manager,
            event_stream=event_stream,
            tool_bridge=tool_bridge,
            enhanced=True
        )
    
    def test_research_agent_initialization(self, research_agent):
        """Test research agent initialization."""
        assert research_agent.company_name == "AAPL"
        assert research_agent.enhanced is True
        assert research_agent.agent_name == "Research Agent"
        assert research_agent.phase_name == "Research Phase"
        assert research_agent.mcp_agent is not None
    
    def test_research_agent_info(self, research_agent):
        """Test research agent info retrieval."""
        info = research_agent.get_agent_info()
        
        assert info["name"] == "Research Agent"
        assert info["type"] == "research"
        assert info["company"] == "AAPL"
        assert info["enhanced"] is True
        assert "Financial data search" in info["capabilities"]
        assert "g-search" in info["tools"]
        assert "fetch" in info["tools"]
    
    @pytest.mark.asyncio
    async def test_research_workflow_execution(self, research_agent):
        """Test research workflow execution with mocking."""
        # Mock the tool bridge execution
        research_agent.tool_bridge.execute_mcp_tool_with_agui_events = AsyncMock(
            return_value="Mock search result"
        )
        
        # Mock context
        mock_context = Mock()
        
        # Execute workflow
        result = await research_agent.execute_agent_workflow(
            thread_id="test_thread",
            context=mock_context
        )
        
        # Verify results
        assert result["status"] == "success"
        assert result["agent"] == "Research Agent"
        assert result["phase"] == "research"
        assert result["company"] == "AAPL"
        assert "results" in result
        assert "metadata" in result
    
    @pytest.mark.asyncio
    async def test_research_queries_execution(self, research_agent):
        """Test research queries execution."""
        # Mock tool bridge
        research_agent.tool_bridge.execute_mcp_tool_with_agui_events = AsyncMock(
            return_value="Mock search result"
        )
        
        # Execute queries
        results = await research_agent._execute_research_queries(
            thread_id="test_thread",
            context=Mock()
        )
        
        # Verify results
        assert len(results) == 5  # Enhanced mode has 5 queries
        for result in results:
            assert "query" in result
            assert "result" in result
            assert "status" in result
    
    @pytest.mark.asyncio
    async def test_research_results_processing(self, research_agent):
        """Test research results processing."""
        # Mock raw results
        raw_results = [
            {"query": "AAPL stock price today", "result": "Mock result", "status": "success"},
            {"query": "AAPL earnings", "result": "Mock result", "status": "success"},
            {"query": "AAPL news", "result": "Mock result", "status": "success"}
        ]
        
        # Process results
        processed = await research_agent._process_research_results(
            raw_results, "test_thread"
        )
        
        # Verify processing
        assert "stock_data" in processed
        assert "earnings_data" in processed
        assert "news_data" in processed
        assert "summary" in processed
        assert processed["summary"]["total_queries"] == 3
        assert processed["summary"]["successful_queries"] == 3


class TestAGUIAnalystAgent:
    """Test AG-UI analyst agent functionality."""
    
    @pytest.fixture
    def setup_components(self):
        """Setup AG-UI components for testing."""
        state_manager = AGUIStateManager()
        event_stream = FinancialAnalysisEventStream()
        tool_bridge = MCPToAGUIToolBridge()
        return state_manager, event_stream, tool_bridge
    
    @pytest.fixture
    def analyst_agent(self, setup_components):
        """Create analyst agent for testing."""
        state_manager, event_stream, tool_bridge = setup_components
        return AGUIAnalystAgent(
            company_name="AAPL",
            state_manager=state_manager,
            event_stream=event_stream,
            tool_bridge=tool_bridge,
            enhanced=True
        )
    
    def test_analyst_agent_initialization(self, analyst_agent):
        """Test analyst agent initialization."""
        assert analyst_agent.company_name == "AAPL"
        assert analyst_agent.enhanced is True
        assert analyst_agent.agent_name == "Analyst Agent"
        assert analyst_agent.phase_name == "Analysis Phase"
        assert analyst_agent.mcp_agent is not None
    
    def test_analyst_agent_info(self, analyst_agent):
        """Test analyst agent info retrieval."""
        info = analyst_agent.get_agent_info()
        
        assert info["name"] == "Analyst Agent"
        assert info["type"] == "analysis"
        assert info["company"] == "AAPL"
        assert info["enhanced"] is True
        assert "Stock performance analysis" in info["capabilities"]
        assert "Risk assessment" in info["capabilities"]
        assert "fetch" in info["tools"]
    
    @pytest.mark.asyncio
    async def test_analyst_workflow_execution(self, analyst_agent):
        """Test analyst workflow execution."""
        # Mock research data
        research_data = {
            "stock_data": [{"query": "AAPL stock", "result": "Mock data"}],
            "earnings_data": [{"query": "AAPL earnings", "result": "Mock data"}],
            "news_data": [{"query": "AAPL news", "result": "Mock data"}]
        }
        
        # Execute workflow
        result = await analyst_agent.execute_agent_workflow(
            thread_id="test_thread",
            research_data=research_data,
            context=Mock()
        )
        
        # Verify results
        assert result["status"] == "success"
        assert result["agent"] == "Analyst Agent"
        assert result["phase"] == "analysis"
        assert result["company"] == "AAPL"
        assert "analysis" in result
        assert "insights" in result
    
    @pytest.mark.asyncio
    async def test_financial_analysis_performance(self, analyst_agent):
        """Test financial analysis performance."""
        research_data = {
            "stock_data": [{"query": "test", "result": "data"}],
            "earnings_data": [{"query": "test", "result": "data"}]
        }
        
        analysis = await analyst_agent._perform_financial_analysis(
            research_data, "test_thread", Mock()
        )
        
        # Verify analysis structure
        assert "stock_performance" in analysis
        assert "earnings_analysis" in analysis
        assert "market_context" in analysis
        assert "risk_assessment" in analysis  # Enhanced mode
    
    @pytest.mark.asyncio
    async def test_stock_performance_analysis(self, analyst_agent):
        """Test stock performance analysis."""
        research_data = {"stock_data": [{"test": "data"}]}
        
        performance = await analyst_agent._analyze_stock_performance(
            research_data, "test_thread"
        )
        
        assert "data_available" in performance
        assert "sources" in performance
        assert performance["data_available"] is True
    
    @pytest.mark.asyncio
    async def test_earnings_analysis(self, analyst_agent):
        """Test earnings analysis."""
        research_data = {"earnings_data": [{"test": "data"}]}
        
        earnings = await analyst_agent._analyze_earnings(
            research_data, "test_thread"
        )
        
        assert "data_available" in earnings
        assert "sources" in earnings
        assert earnings["data_available"] is True
    
    @pytest.mark.asyncio
    async def test_insights_generation(self, analyst_agent):
        """Test insights generation."""
        analysis_results = {
            "stock_performance": {"trend": "positive"},
            "earnings_analysis": {"beat_expectations": True}
        }
        
        insights = await analyst_agent._generate_insights(
            analysis_results, "test_thread"
        )
        
        assert "strengths" in insights
        assert "concerns" in insights
        assert "recommendation" in insights
        assert "confidence_level" in insights


class TestAGUIReportWriter:
    """Test AG-UI report writer functionality."""
    
    @pytest.fixture
    def setup_components(self):
        """Setup AG-UI components for testing."""
        state_manager = AGUIStateManager()
        event_stream = FinancialAnalysisEventStream()
        tool_bridge = MCPToAGUIToolBridge()
        return state_manager, event_stream, tool_bridge
    
    @pytest.fixture
    def report_writer(self, setup_components, tmp_path):
        """Create report writer for testing."""
        state_manager, event_stream, tool_bridge = setup_components
        output_path = str(tmp_path / "test_report.md")
        return AGUIReportWriter(
            company_name="AAPL",
            output_path=output_path,
            state_manager=state_manager,
            event_stream=event_stream,
            tool_bridge=tool_bridge
        )
    
    def test_report_writer_initialization(self, report_writer):
        """Test report writer initialization."""
        assert report_writer.company_name == "AAPL"
        assert report_writer.output_path.endswith("test_report.md")
        assert report_writer.agent_name == "Report Writer"
        assert report_writer.phase_name == "Report Generation"
        assert report_writer.mcp_agent is not None
    
    def test_report_writer_info(self, report_writer):
        """Test report writer info retrieval."""
        info = report_writer.get_agent_info()
        
        assert info["name"] == "Report Writer"
        assert info["type"] == "reporting"
        assert info["company"] == "AAPL"
        assert "Report generation" in info["capabilities"]
        assert "filesystem" in info["tools"]
    
    @pytest.mark.asyncio
    async def test_report_workflow_execution(self, report_writer):
        """Test report generation workflow."""
        # Mock data
        research_data = {"stock_data": [], "earnings_data": []}
        analysis_data = {"analysis": {}, "insights": {"recommendation": "BUY"}}
        
        # Execute workflow
        result = await report_writer.execute_agent_workflow(
            thread_id="test_thread",
            research_data=research_data,
            analysis_data=analysis_data,
            context=Mock()
        )
        
        # Verify results
        assert result["status"] == "success"
        assert result["agent"] == "Report Writer"
        assert result["phase"] == "reporting"
        assert result["company"] == "AAPL"
        assert "report_path" in result
        assert "report_content" in result
    
    @pytest.mark.asyncio
    async def test_report_sections_generation(self, report_writer):
        """Test report sections generation."""
        research_data = {"stock_data": [], "earnings_data": []}
        analysis_data = {"analysis": {}, "insights": {"recommendation": "HOLD"}}
        
        sections = await report_writer._generate_report_sections(
            research_data, analysis_data, "test_thread", Mock()
        )
        
        # Verify sections
        expected_sections = [
            "Executive Summary",
            "Company Overview",
            "Financial Performance",
            "Market Analysis",
            "Investment Recommendation",
            "Risk Assessment",
            "Conclusion"
        ]
        
        for section in expected_sections:
            assert section in sections
            assert len(sections[section]) > 0
    
    @pytest.mark.asyncio
    async def test_section_content_generation(self, report_writer):
        """Test individual section content generation."""
        research_data = {"stock_data": [1, 2], "earnings_data": [1]}
        analysis_data = {"insights": {"recommendation": "BUY"}}
        
        # Test executive summary
        content = await report_writer._generate_section_content(
            "Executive Summary", research_data, analysis_data, "test_thread"
        )
        
        assert "Executive Summary" in content
        assert "AAPL" in content
        assert "BUY" in content
    
    @pytest.mark.asyncio
    async def test_report_saving(self, report_writer):
        """Test report saving functionality."""
        report_content = {
            "Executive Summary": "# Executive Summary\nTest content",
            "Conclusion": "# Conclusion\nTest conclusion"
        }
        
        # Save report
        saved_path = await report_writer._save_report(
            report_content, "test_thread"
        )
        
        # Verify file was created
        assert saved_path == report_writer.output_path
        
        # Verify file content
        with open(saved_path, 'r') as f:
            content = f.read()
            assert "AAPL" in content
            assert "Executive Summary" in content
            assert "Conclusion" in content
