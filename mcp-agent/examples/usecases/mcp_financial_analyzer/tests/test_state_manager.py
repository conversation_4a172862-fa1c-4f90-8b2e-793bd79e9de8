"""
Unit Tests for AG-UI State Manager
=================================

Tests for state management, JSON Patch generation, and observer notifications.
"""

import pytest
from unittest.mock import Mock, call
from datetime import datetime

from ag_ui_sample.state_manager import AGUIStateManager
from ag_ui_sample.events import StateDeltaEvent, StateSnapshotEvent


class TestAGUIStateManager:
    """Unit tests for AG-UI state manager"""
    
    def test_default_state_initialization(self):
        """Test state manager initializes with correct default state"""
        # Execute
        state_manager = AGUIStateManager()
        
        # Verify
        state = state_manager.current_state
        assert state["analysis_phase"] == "initialized"
        assert state["progress_percentage"] == 0.0
        assert state["company_name"] == ""
        assert state["current_operation"] == "initialization"
        assert state["research_data"] == {}
        assert state["analysis_results"] == {}
        assert state["report_status"] == "not_started"
        assert state["error_state"] is None
        assert "start_time" in state
        assert "last_updated" in state
        assert "agents_status" in state
        assert state["agents_status"]["research_agent"] == "idle"
        assert state["agents_status"]["analyst_agent"] == "idle"
        assert state["agents_status"]["report_writer"] == "idle"
        assert state["tool_calls"] == []
        assert state["messages"] == []
    
    def test_custom_initial_state(self):
        """Test state manager with custom initial state"""
        # Setup
        custom_state = {
            "analysis_phase": "custom_phase",
            "company_name": "Test Company",
            "progress_percentage": 50.0
        }
        
        # Execute
        state_manager = AGUIStateManager(custom_state)
        
        # Verify
        assert state_manager.current_state == custom_state
    
    def test_observer_management(self):
        """Test adding and removing observers"""
        # Setup
        state_manager = AGUIStateManager()
        observer1 = Mock()
        observer2 = Mock()
        
        # Test adding observers
        state_manager.add_observer(observer1)
        state_manager.add_observer(observer2)
        assert len(state_manager.observers) == 2
        
        # Test removing observer
        state_manager.remove_observer(observer1)
        assert len(state_manager.observers) == 1
        assert observer2 in state_manager.observers
        assert observer1 not in state_manager.observers
    
    def test_state_update_with_json_patch(self):
        """Test state updates generate correct JSON Patch operations"""
        # Setup
        state_manager = AGUIStateManager()
        thread_id = "test_thread_001"
        
        # Execute
        updates = {
            "analysis_phase": "research",
            "progress_percentage": 25.0,
            "company_name": "Apple Inc."
        }
        patch_operations = state_manager.update_state(updates, thread_id, emit_event=False)
        
        # Verify state was updated
        assert state_manager.current_state["analysis_phase"] == "research"
        assert state_manager.current_state["progress_percentage"] == 25.0
        assert state_manager.current_state["company_name"] == "Apple Inc."
        
        # Verify JSON Patch operations
        assert len(patch_operations) >= 3  # At least the 3 updates plus last_updated
        
        # Find specific operations
        operations_by_path = {op["path"]: op for op in patch_operations}
        
        assert operations_by_path["/analysis_phase"]["op"] == "replace"
        assert operations_by_path["/analysis_phase"]["value"] == "research"
        
        assert operations_by_path["/progress_percentage"]["op"] == "replace"
        assert operations_by_path["/progress_percentage"]["value"] == 25.0
        
        assert operations_by_path["/company_name"]["op"] == "replace"
        assert operations_by_path["/company_name"]["value"] == "Apple Inc."
    
    def test_state_update_with_observer_notification(self):
        """Test state updates notify observers with STATE_DELTA event"""
        # Setup
        state_manager = AGUIStateManager()
        observer = Mock()
        state_manager.add_observer(observer)
        thread_id = "test_thread_002"
        
        # Execute
        updates = {"analysis_phase": "analysis", "progress_percentage": 75.0}
        state_manager.update_state(updates, thread_id)
        
        # Verify observer was called
        observer.assert_called_once()
        
        # Verify the event passed to observer
        call_args = observer.call_args[0][0]
        assert isinstance(call_args, StateDeltaEvent)
        assert call_args.threadId == thread_id
        assert call_args.type == "STATE_DELTA"
        assert len(call_args.delta) >= 2  # At least the 2 updates plus last_updated
    
    def test_analysis_phase_update(self):
        """Test set_analysis_phase method"""
        # Setup
        state_manager = AGUIStateManager()
        observer = Mock()
        state_manager.add_observer(observer)
        thread_id = "test_thread_003"
        
        # Execute
        state_manager.set_analysis_phase("research", thread_id, progress=30.0)
        
        # Verify
        assert state_manager.current_state["analysis_phase"] == "research"
        assert state_manager.current_state["progress_percentage"] == 30.0
        observer.assert_called_once()
    
    def test_agent_status_update(self):
        """Test set_agent_status method"""
        # Setup
        state_manager = AGUIStateManager()
        observer = Mock()
        state_manager.add_observer(observer)
        thread_id = "test_thread_004"
        
        # Execute
        state_manager.set_agent_status("research_agent", "running", thread_id)
        
        # Verify
        assert state_manager.current_state["agents_status"]["research_agent"] == "running"
        assert state_manager.current_state["agents_status"]["analyst_agent"] == "idle"  # Others unchanged
        observer.assert_called_once()
    
    def test_tool_call_addition(self):
        """Test add_tool_call method"""
        # Setup
        state_manager = AGUIStateManager()
        observer = Mock()
        state_manager.add_observer(observer)
        thread_id = "test_thread_005"
        
        # Execute
        tool_call_info = {
            "tool_name": "search_financial_data",
            "args": {"query": "Apple stock price"},
            "status": "completed"
        }
        state_manager.add_tool_call(tool_call_info, thread_id)
        
        # Verify
        tool_calls = state_manager.current_state["tool_calls"]
        assert len(tool_calls) == 1
        assert tool_calls[0]["tool_name"] == "search_financial_data"
        assert tool_calls[0]["args"]["query"] == "Apple stock price"
        assert "timestamp" in tool_calls[0]
        observer.assert_called_once()
    
    def test_error_state_management(self):
        """Test error state setting and clearing"""
        # Setup
        state_manager = AGUIStateManager()
        observer = Mock()
        state_manager.add_observer(observer)
        thread_id = "test_thread_006"
        
        # Test setting error state
        error_info = {
            "type": "APIError",
            "message": "Search API unavailable",
            "agent": "research_agent"
        }
        state_manager.set_error_state(error_info, thread_id)
        
        # Verify error state was set
        assert state_manager.current_state["error_state"] == error_info
        assert observer.call_count == 1
        
        # Test clearing error state
        state_manager.clear_error_state(thread_id)
        
        # Verify error state was cleared
        assert state_manager.current_state["error_state"] is None
        assert observer.call_count == 2
    
    def test_state_snapshot(self):
        """Test get_state_snapshot method"""
        # Setup
        state_manager = AGUIStateManager()
        observer = Mock()
        state_manager.add_observer(observer)
        thread_id = "test_thread_007"
        
        # Update some state first
        state_manager.update_state({
            "analysis_phase": "reporting",
            "progress_percentage": 90.0
        }, thread_id, emit_event=False)
        
        # Execute
        snapshot = state_manager.get_state_snapshot(thread_id)
        
        # Verify
        assert snapshot["analysis_phase"] == "reporting"
        assert snapshot["progress_percentage"] == 90.0
        
        # Verify observer was called with STATE_SNAPSHOT event
        observer.assert_called_once()
        call_args = observer.call_args[0][0]
        assert isinstance(call_args, StateSnapshotEvent)
        assert call_args.threadId == thread_id
        assert call_args.type == "STATE_SNAPSHOT"
        assert call_args.state == snapshot
    
    def test_progress_info(self):
        """Test get_progress_info method"""
        # Setup
        state_manager = AGUIStateManager()
        state_manager.update_state({
            "analysis_phase": "analysis",
            "progress_percentage": 60.0,
            "current_operation": "calculating_financial_ratios",
            "agents_status": {"research_agent": "completed", "analyst_agent": "running"}
        }, "test_thread", emit_event=False)
        
        # Execute
        progress_info = state_manager.get_progress_info()
        
        # Verify
        assert progress_info["phase"] == "analysis"
        assert progress_info["progress"] == 60.0
        assert progress_info["operation"] == "calculating_financial_ratios"
        assert progress_info["agents_status"]["research_agent"] == "completed"
        assert progress_info["agents_status"]["analyst_agent"] == "running"
        assert progress_info["error_state"] is None
    
    def test_state_history_tracking(self):
        """Test that state history is maintained"""
        # Setup
        state_manager = AGUIStateManager()
        thread_id = "test_thread_008"
        
        # Make multiple updates
        state_manager.update_state({"analysis_phase": "research"}, thread_id, emit_event=False)
        state_manager.update_state({"progress_percentage": 50.0}, thread_id, emit_event=False)
        state_manager.update_state({"analysis_phase": "analysis"}, thread_id, emit_event=False)
        
        # Verify
        assert len(state_manager.state_history) == 3
        
        # Verify history contains previous states
        assert state_manager.state_history[0]["analysis_phase"] == "initialized"
        assert state_manager.state_history[1]["analysis_phase"] == "research"
        assert state_manager.state_history[2]["analysis_phase"] == "research"  # Still research phase
        assert state_manager.state_history[2]["progress_percentage"] == 50.0  # Updated progress
    
    def test_state_reset(self):
        """Test state reset functionality"""
        # Setup
        state_manager = AGUIStateManager()
        observer = Mock()
        state_manager.add_observer(observer)
        thread_id = "test_thread_009"
        
        # Make some updates first
        state_manager.update_state({
            "analysis_phase": "completed",
            "progress_percentage": 100.0,
            "company_name": "Test Company"
        }, thread_id, emit_event=False)
        
        # Add some history
        assert len(state_manager.state_history) == 1
        
        # Execute reset
        state_manager.reset_state(thread_id)
        
        # Verify state was reset to defaults
        assert state_manager.current_state["analysis_phase"] == "initialized"
        assert state_manager.current_state["progress_percentage"] == 0.0
        assert state_manager.current_state["company_name"] == ""
        
        # Verify history was cleared
        assert len(state_manager.state_history) == 0
        
        # Verify observer was notified with STATE_SNAPSHOT
        observer.assert_called_once()
        call_args = observer.call_args[0][0]
        assert isinstance(call_args, StateSnapshotEvent)
    
    def test_observer_error_handling(self):
        """Test that observer errors don't break state updates"""
        # Setup
        state_manager = AGUIStateManager()
        
        # Add observer that raises exception
        failing_observer = Mock(side_effect=Exception("Observer error"))
        working_observer = Mock()
        
        state_manager.add_observer(failing_observer)
        state_manager.add_observer(working_observer)
        
        # Execute - should not raise exception
        state_manager.update_state({"analysis_phase": "test"}, "test_thread")
        
        # Verify both observers were called despite one failing
        failing_observer.assert_called_once()
        working_observer.assert_called_once()
        
        # Verify state was still updated
        assert state_manager.current_state["analysis_phase"] == "test"
