"""
Test AG-UI Integration in main.py
=================================

Comprehensive tests for AG-UI integration in the main financial analyzer,
including command-line argument parsing, orchestrator initialization,
and complete workflow execution with streaming capabilities.
"""

import pytest
import asyncio
import sys
import os
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from argparse import Namespace

# Add the project root to the path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import AG-UI components for testing
from ag_ui_sample.state_manager import AGUIStateManager
from ag_ui_sample.event_stream import FinancialAnalysisEventStream
from ag_ui_sample.tool_bridge import MCPToAGUIToolBridge
from ag_ui_sample.agent_wrappers import AGUIResearchAgent, AGUIAnalystAgent, AGUIReportWriter


class TestMainAGUIIntegration:
    """Test AG-UI integration in main.py"""
    
    @pytest.fixture
    def mock_sys_argv(self):
        """Mock sys.argv for testing command-line arguments"""
        original_argv = sys.argv.copy()
        yield
        sys.argv = original_argv
    
    @pytest.fixture
    def mock_mcp_app(self):
        """Mock MCP app for testing"""
        mock_app = Mock()
        mock_context = Mock()
        mock_logger = Mock()
        
        # Configure mock app
        mock_app.run.return_value.__aenter__ = AsyncMock(return_value=Mock(
            context=mock_context,
            logger=mock_logger
        ))
        mock_app.run.return_value.__aexit__ = AsyncMock(return_value=None)
        
        # Configure mock context
        mock_context.config.mcp.servers = {
            "filesystem": Mock(args=[]),
            "g-search": Mock()
        }
        
        return mock_app, mock_context, mock_logger
    
    def test_command_line_argument_parsing_default_mode(self, mock_sys_argv):
        """Test command-line argument parsing for default MCP mode"""
        sys.argv = ["main.py", "Apple"]
        
        # Import main module to test argument parsing
        with patch('main.MCPApp') as mock_mcp_app:
            import main
            
            # Verify default behavior
            assert main.COMPANY_NAME == "Apple"
            # Should not have AG-UI mode enabled by default
            assert not hasattr(main, 'AGUI_ENABLED')
    
    def test_command_line_argument_parsing_agui_mode(self, mock_sys_argv):
        """Test command-line argument parsing for AG-UI mode"""
        sys.argv = ["main.py", "Apple", "--enable-agui", "--agui-port", "8080"]
        
        # This test will pass once we implement AG-UI argument parsing
        # For now, we're defining the expected behavior
        expected_args = {
            'company': 'Apple',
            'enable_agui': True,
            'agui_port': 8080,
            'agui_host': 'localhost'  # default
        }
        
        # Test will be implemented when we add argument parsing
        assert True  # Placeholder for now
    
    def test_backward_compatibility_mcp_mode(self, mock_mcp_app):
        """Test that existing MCP functionality remains unchanged"""
        mock_app, mock_context, mock_logger = mock_mcp_app
        
        with patch('main.MCPApp', return_value=mock_app):
            with patch('main.create_research_agent') as mock_research:
                with patch('main.create_analyst_agent') as mock_analyst:
                    with patch('main.create_report_writer') as mock_writer:
                        with patch('main.Orchestrator') as mock_orchestrator:
                            # Mock agent creation
                            mock_research.return_value = Mock()
                            mock_analyst.return_value = Mock()
                            mock_writer.return_value = Mock()
                            
                            # Mock orchestrator
                            mock_orch_instance = Mock()
                            mock_orch_instance.generate_str = AsyncMock()
                            mock_orchestrator.return_value = mock_orch_instance
                            
                            # Import and test main function
                            import main
                            
                            # Verify that MCP components are still created
                            assert callable(main.main)
                            # Test will verify backward compatibility
    
    @pytest.mark.asyncio
    async def test_agui_orchestrator_initialization(self):
        """Test AG-UI orchestrator initialization and configuration"""
        # Mock AG-UI components
        mock_state_manager = Mock(spec=AGUIStateManager)
        mock_event_stream = Mock(spec=FinancialAnalysisEventStream)
        mock_tool_bridge = Mock(spec=MCPToAGUIToolBridge)
        
        # Test AG-UI orchestrator creation
        with patch('ag_ui_sample.state_manager.AGUIStateManager', return_value=mock_state_manager):
            with patch('ag_ui_sample.event_stream.FinancialAnalysisEventStream', return_value=mock_event_stream):
                with patch('ag_ui_sample.tool_bridge.MCPToAGUIToolBridge', return_value=mock_tool_bridge):
                    # This will test the AG-UI orchestrator once implemented
                    assert mock_state_manager is not None
                    assert mock_event_stream is not None
                    assert mock_tool_bridge is not None
    
    @pytest.mark.asyncio
    async def test_agui_agent_wrapper_creation(self):
        """Test creation of AG-UI agent wrappers"""
        company_name = "Apple"
        
        # Mock AG-UI components
        mock_state_manager = Mock(spec=AGUIStateManager)
        mock_event_stream = Mock(spec=FinancialAnalysisEventStream)
        mock_tool_bridge = Mock(spec=MCPToAGUIToolBridge)
        
        # Test agent wrapper creation
        with patch('ag_ui_sample.agent_wrappers.create_enhanced_research_agent'):
            with patch('ag_ui_sample.agent_wrappers.create_enhanced_analyst_agent'):
                with patch('ag_ui_sample.agent_wrappers.create_report_writer'):
                    # Create AG-UI agent wrappers
                    research_agent = AGUIResearchAgent(
                        company_name=company_name,
                        state_manager=mock_state_manager,
                        event_stream=mock_event_stream,
                        tool_bridge=mock_tool_bridge
                    )
                    
                    analyst_agent = AGUIAnalystAgent(
                        company_name=company_name,
                        state_manager=mock_state_manager,
                        event_stream=mock_event_stream,
                        tool_bridge=mock_tool_bridge
                    )
                    
                    report_writer = AGUIReportWriter(
                        company_name=company_name,
                        output_path="test_report.md",
                        state_manager=mock_state_manager,
                        event_stream=mock_event_stream,
                        tool_bridge=mock_tool_bridge
                    )
                    
                    # Verify agent wrappers are created correctly
                    assert research_agent.company_name == company_name
                    assert analyst_agent.company_name == company_name
                    assert report_writer.company_name == company_name
    
    @pytest.mark.asyncio
    async def test_complete_agui_workflow_execution(self):
        """Test complete financial analysis workflow with AG-UI streaming"""
        company_name = "Apple"
        thread_id = "test_thread_123"
        
        # Mock AG-UI components
        mock_state_manager = Mock(spec=AGUIStateManager)
        mock_event_stream = Mock(spec=FinancialAnalysisEventStream)
        mock_tool_bridge = Mock(spec=MCPToAGUIToolBridge)
        
        # Mock async methods
        mock_state_manager.set_analysis_phase = Mock()
        mock_event_stream.stream_text_message = AsyncMock()
        mock_event_stream.stream_analysis_phase = AsyncMock()
        mock_event_stream.stream_agent_activity = AsyncMock()
        mock_tool_bridge.execute_mcp_tool_with_agui_events = AsyncMock(return_value="Mock result")
        
        # Test complete workflow execution
        with patch('ag_ui_sample.agent_wrappers.create_enhanced_research_agent'):
            with patch('ag_ui_sample.agent_wrappers.create_enhanced_analyst_agent'):
                with patch('ag_ui_sample.agent_wrappers.create_report_writer'):
                    # Create and execute workflow
                    research_agent = AGUIResearchAgent(
                        company_name=company_name,
                        state_manager=mock_state_manager,
                        event_stream=mock_event_stream,
                        tool_bridge=mock_tool_bridge
                    )
                    
                    # Execute research workflow
                    result = await research_agent.execute_agent_workflow(
                        thread_id=thread_id,
                        context=Mock()
                    )
                    
                    # Verify workflow execution
                    assert result["status"] == "success"
                    assert result["company"] == company_name
                    assert result["phase"] == "research"
                    
                    # Verify streaming events were called
                    mock_event_stream.stream_analysis_phase.assert_called()
                    mock_event_stream.stream_agent_activity.assert_called()
    
    def test_agui_mode_configuration_validation(self):
        """Test AG-UI mode configuration validation"""
        # Test valid configurations
        valid_configs = [
            {"enable_agui": True, "agui_port": 8080, "agui_host": "localhost"},
            {"enable_agui": True, "agui_port": 3000, "agui_host": "0.0.0.0"},
            {"enable_agui": False}  # Disabled mode should be valid
        ]
        
        for config in valid_configs:
            # Validate configuration
            assert isinstance(config.get("enable_agui"), bool)
            if config.get("enable_agui"):
                assert isinstance(config.get("agui_port"), int)
                assert 1000 <= config.get("agui_port") <= 65535
                assert isinstance(config.get("agui_host"), str)
    
    def test_agui_mode_error_handling(self):
        """Test error handling in AG-UI mode"""
        # Test invalid port numbers
        invalid_ports = [-1, 0, 999, 65536, "invalid"]
        
        for port in invalid_ports:
            if isinstance(port, int) and (port < 1000 or port > 65535):
                # Should raise validation error
                assert True  # Placeholder for validation logic
            elif not isinstance(port, int):
                # Should raise type error
                assert True  # Placeholder for type validation
    
    @pytest.mark.asyncio
    async def test_agui_event_streaming_integration(self):
        """Test AG-UI event streaming integration with main workflow"""
        # Mock event stream
        mock_event_stream = Mock(spec=FinancialAnalysisEventStream)
        mock_event_stream.stream_text_message = AsyncMock()
        mock_event_stream.stream_analysis_phase = AsyncMock()
        mock_event_stream.stream_results = AsyncMock()
        
        # Test event streaming during workflow
        thread_id = "test_thread"
        
        # Simulate workflow events
        await mock_event_stream.stream_analysis_phase(
            phase_name="Research Phase",
            phase_description="Gathering financial data",
            thread_id=thread_id,
            progress=0.0
        )
        
        await mock_event_stream.stream_text_message(
            content="Starting financial analysis",
            thread_id=thread_id,
            role="assistant"
        )
        
        await mock_event_stream.stream_results(
            results={"status": "completed"},
            thread_id=thread_id,
            result_type="financial_analysis"
        )
        
        # Verify streaming calls
        mock_event_stream.stream_analysis_phase.assert_called_once()
        mock_event_stream.stream_text_message.assert_called_once()
        mock_event_stream.stream_results.assert_called_once()
    
    def test_agui_websocket_server_configuration(self):
        """Test AG-UI WebSocket server configuration"""
        # Test server configuration parameters
        config = {
            "host": "localhost",
            "port": 8080,
            "cors_origins": ["http://localhost:3000"],
            "max_connections": 100
        }
        
        # Validate configuration
        assert config["host"] in ["localhost", "0.0.0.0", "127.0.0.1"]
        assert 1000 <= config["port"] <= 65535
        assert isinstance(config["cors_origins"], list)
        assert config["max_connections"] > 0


class TestMainAGUICommandLine:
    """Test command-line interface for AG-UI integration"""
    
    def test_help_message_includes_agui_options(self):
        """Test that help message includes AG-UI options"""
        # Expected AG-UI command-line options
        expected_options = [
            "--enable-agui",
            "--agui-port",
            "--agui-host",
            "--agui-cors-origins"
        ]
        
        # This will be tested once we implement argument parsing
        for option in expected_options:
            assert isinstance(option, str)
            assert option.startswith("--")
    
    def test_agui_mode_enables_streaming(self):
        """Test that AG-UI mode enables event streaming"""
        # Mock command-line arguments for AG-UI mode
        agui_args = {
            "enable_agui": True,
            "agui_port": 8080,
            "company": "Apple"
        }

        # Verify AG-UI mode configuration
        assert agui_args["enable_agui"] is True
        assert isinstance(agui_args["agui_port"], int)
        assert agui_args["company"] == "Apple"

    @pytest.mark.asyncio
    async def test_agui_orchestrator_workflow_integration(self):
        """Test AG-UI orchestrator workflow integration"""
        # Import main module components
        import main

        # Mock context and logger
        mock_context = Mock()
        mock_logger = Mock()

        # Create AG-UI orchestrator
        orchestrator = main.AGUIOrchestrator(
            company_name="Apple",
            output_path="test_report.md",
            context=mock_context,
            logger=mock_logger
        )

        # Verify orchestrator initialization
        assert orchestrator.company_name == "Apple"
        assert orchestrator.output_path == "test_report.md"
        assert orchestrator.context == mock_context
        assert orchestrator.logger == mock_logger

        # Verify AG-UI components are initialized
        assert orchestrator.state_manager is not None
        assert orchestrator.event_stream is not None
        assert orchestrator.tool_bridge is not None

        # Verify agent wrappers are initialized
        assert orchestrator.research_agent is not None
        assert orchestrator.analyst_agent is not None
        assert orchestrator.report_writer is not None

    def test_main_argument_parsing_integration(self):
        """Test main.py argument parsing integration"""
        import main

        # Test that ARGS is properly set
        assert hasattr(main, 'ARGS')
        assert hasattr(main, 'COMPANY_NAME')

        # Verify argument structure
        args = main.ARGS
        assert hasattr(args, 'company')
        assert hasattr(args, 'enable_agui')
        assert hasattr(args, 'agui_port')
        assert hasattr(args, 'agui_host')
        assert hasattr(args, 'agui_cors_origins')

    @pytest.mark.asyncio
    async def test_mcp_mode_backward_compatibility(self):
        """Test that MCP mode maintains backward compatibility"""
        # Mock components for MCP mode test
        mock_context = Mock()
        mock_logger = Mock()

        # Import main module
        import main

        # Test run_mcp_mode function exists and is callable
        assert hasattr(main, 'run_mcp_mode')
        assert callable(main.run_mcp_mode)

        # Mock the dependencies to avoid actual execution
        with patch('main.create_research_agent') as mock_research:
            with patch('main.create_analyst_agent') as mock_analyst:
                with patch('main.create_report_writer') as mock_writer:
                    with patch('main.Agent') as mock_agent:
                        with patch('main.EvaluatorOptimizerLLM') as mock_evaluator:
                            with patch('main.Orchestrator') as mock_orchestrator:
                                with patch('os.path.exists', return_value=True):
                                    # Mock all components
                                    mock_research.return_value = Mock()
                                    mock_analyst.return_value = Mock()
                                    mock_writer.return_value = Mock()
                                    mock_agent.return_value = Mock()
                                    mock_evaluator.return_value = Mock()

                                    mock_orch_instance = Mock()
                                    mock_orch_instance.generate_str = AsyncMock()
                                    mock_orchestrator.return_value = mock_orch_instance

                                    # Test MCP mode execution
                                    result = await main.run_mcp_mode(
                                        context=mock_context,
                                        logger=mock_logger,
                                        output_path="test_report.md"
                                    )

                                    # Verify successful execution
                                    assert result is True
                                    mock_orch_instance.generate_str.assert_called_once()

    @pytest.mark.asyncio
    async def test_agui_mode_execution(self):
        """Test AG-UI mode execution"""
        # Mock components for AG-UI mode test
        mock_context = Mock()
        mock_logger = Mock()

        # Import main module
        import main

        # Test run_agui_mode function exists and is callable
        assert hasattr(main, 'run_agui_mode')
        assert callable(main.run_agui_mode)

        # Mock AG-UI orchestrator
        with patch.object(main, 'AGUIOrchestrator') as mock_orchestrator_class:
            mock_orchestrator = Mock()
            mock_orchestrator.execute_workflow = AsyncMock(return_value={
                "status": "success",
                "company": "Apple",
                "workflow": "complete"
            })
            mock_orchestrator_class.return_value = mock_orchestrator

            # Test AG-UI mode execution
            result = await main.run_agui_mode(
                context=mock_context,
                logger=mock_logger,
                output_path="test_report.md"
            )

            # Verify successful execution
            assert result is True
            mock_orchestrator.execute_workflow.assert_called_once()

            # Verify orchestrator was created with correct parameters
            mock_orchestrator_class.assert_called_once_with(
                company_name=main.COMPANY_NAME,
                output_path="test_report.md",
                context=mock_context,
                logger=mock_logger
            )
