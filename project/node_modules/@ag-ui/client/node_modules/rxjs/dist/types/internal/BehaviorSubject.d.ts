import { Subject } from './Subject';
/**
 * A variant of Subject that requires an initial value and emits its current
 * value whenever it is subscribed to.
 *
 * @class BehaviorSubject<T>
 */
export declare class BehaviorSubject<T> extends Subject<T> {
    private _value;
    constructor(_value: T);
    get value(): T;
    getValue(): T;
    next(value: T): void;
}
//# sourceMappingURL=BehaviorSubject.d.ts.map